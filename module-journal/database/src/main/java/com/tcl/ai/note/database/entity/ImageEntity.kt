package com.tcl.ai.note.database.entity

import android.content.ContentUris
import android.net.Uri
import android.provider.MediaStore
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = DBConst.TABLE_NAME_IMAGE)
data class Image(
    @PrimaryKey
    @ColumnInfo(name = ImageEntity.IMAGE_ID) val imageId: String,
    @ColumnInfo(name = ImageEntity.PATH) val path: String,
    @ColumnInfo(name = ImageEntity.SIZE) var size: Long = 0L,
    @ColumnInfo(name = ImageEntity.CREATE_TIME) var createTime: Long = 0L,
    @ColumnInfo(name = ImageEntity.MODIFY_TIME) var modifyTime: Long = 0L,
    @ColumnInfo(name = ImageEntity.DATE_TIME) var dateTime: String? = null,
    @ColumnInfo(name = ImageEntity.LATITUDE) var latitude: Double = 0.0,
    @ColumnInfo(name = ImageEntity.LONGITUDE) var longitude: Double = 0.0,
    @ColumnInfo(name = ImageEntity.LOCATION) var location: String? = null,
    @ColumnInfo(name = ImageEntity.QUALITY) var quality: Double = 0.0,
    @ColumnInfo(name = ImageEntity.USER_OPERATOR) var userOperator: Boolean = false,
    @ColumnInfo(name = ImageEntity.FACE_DETECT) var faceDetect: Boolean = false,
    @ColumnInfo(name = ImageEntity.SCORE) var score: Double = 0.0,
    @ColumnInfo(name = ImageEntity.MARKED) var marked: Boolean = false,
    @ColumnInfo(name = ImageEntity.ALBUM_ID) var albumId: String? = null
)

object ImageEntity {
    const val IMAGE_ID = "image_id"
    const val PATH = "path"
    const val SIZE = "size"
    const val CREATE_TIME = "create_time"
    const val MODIFY_TIME = "modify_time"
    const val DATE_TIME = "date_time"
    const val LATITUDE = "latitude"
    const val LONGITUDE = "longitude"
    const val LOCATION = "location"
    const val QUALITY = "quality"
    const val USER_OPERATOR = "user_operator"
    const val FACE_DETECT = "face_detect"
    const val SCORE = "score"
    const val MARKED = "marked"
    const val ALBUM_ID = "album_id"
}