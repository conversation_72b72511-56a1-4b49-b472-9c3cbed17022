package com.tcl.ai.note.database.repository

import com.tcl.ai.note.database.IoDispatcher
import com.tcl.ai.note.database.dao.JournalContentDao
import com.tcl.ai.note.database.entity.JournalContent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 临时创建，用于JournalContent数据库创建的参考
 */
class JournalContentRepository @Inject constructor(
    private val dao: JournalContentDao,
    @IoDispatcher private val dispatcher: CoroutineDispatcher // 添加限定符
) {

    /**
     * 获取一条JournalContent
     */
    suspend fun getJournalContentById(journalId: Long): JournalContent? = withContext(dispatcher) {
        return@withContext dao.getJournalContentById(journalId)
    }

    /**
     * 插入JournalContent
     */
    suspend fun insertJournalContent(content: JournalContent): Long = withContext(dispatcher) {
        dao.insert(content)
    }

    /**
     * 更新JournalContent
     */
    suspend fun updateJournalContent(content: JournalContent): Long = withContext(dispatcher) {
        dao.update(content)
        content.journalId
    }

    /**
     * 删除一个JournalContent
     */
    suspend fun deleteJournalContent(journalId: Long): Int = withContext(dispatcher) {
        dao.delete(journalId)
    }

    /**
     * 删除JournalContent列表
     */
    suspend fun deleteJournalContents(journalIds: List<Long>): Int = withContext(dispatcher) {
        dao.deleteContents(journalIds)
    }
}