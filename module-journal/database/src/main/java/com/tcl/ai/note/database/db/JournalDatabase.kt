package com.tcl.ai.note.database.db

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.util.Pair
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.tcl.ai.note.base.R
import com.tcl.ai.note.database.convertor.PageInfoConvertor
import com.tcl.ai.note.database.dao.AlbumDao
import com.tcl.ai.note.database.dao.CategoryDao
import com.tcl.ai.note.database.dao.ImageDao
import com.tcl.ai.note.database.dao.ImageEmbeddingDao
import com.tcl.ai.note.database.dao.JournalContentDao
import com.tcl.ai.note.database.dao.JournalDao
import com.tcl.ai.note.database.entity.Album
import com.tcl.ai.note.database.entity.AlbumEntity
import com.tcl.ai.note.database.entity.DBConst
import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.database.entity.ImageEmbedding
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.database.entity.JournalContent
import com.tcl.ai.note.database.entity.PageInfo
import com.tcl.ai.note.journalbase.copySampleAssetsToExternal
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import java.io.File

@Database(
    entities = [Journal::class, JournalCategory::class, Image::class, Album::class, JournalContent::class, ImageEmbedding::class],
    version = 3,
    autoMigrations = [],
    exportSchema = true
)
abstract class JournalDatabase : RoomDatabase() {

    abstract fun noteDao(): JournalDao
    abstract fun categoryDao(): CategoryDao
    abstract fun journalContentDao(): JournalContentDao
    abstract fun imageDao(): ImageDao
    abstract fun albumDao(): AlbumDao
    abstract fun imageEmbeddingDao(): ImageEmbeddingDao

    companion object {

        @Volatile
        private var INSTANCE: JournalDatabase? = null

        @JvmStatic
        fun getInstance(context: Context): JournalDatabase {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: buildDataBase(context).also { INSTANCE = it }
            }
        }

        private fun buildDataBase(context: Context): JournalDatabase {
            return Room.databaseBuilder(context, JournalDatabase::class.java, DBConst.DB_NAME)
                .addMigrations(object : Migration(1, 2) {
                    override fun migrate(db: SupportSQLiteDatabase) {
                        db.execSQL("ALTER TABLE ${DBConst.TABLE_NAME_ALBUM} ADD COLUMN ${AlbumEntity.ALBUM_NAME} TEXT")
                        db.execSQL("DELETE FROM ${DBConst.TABLE_NAME_ALBUM}")
                        db.execSQL("DELETE FROM image")
                    }
                })
                .addMigrations(object : Migration(2, 3) {
                    override fun migrate(db: SupportSQLiteDatabase) {
                        db.execSQL("""
                            CREATE TABLE IF NOT EXISTS image_embedding (
                                image_id TEXT NOT NULL PRIMARY KEY,
                                float_embedding BLOB NOT NULL,
                                quantized_embedding BLOB NOT NULL,
                                head_index INTEGER NOT NULL,
                                head_name TEXT NOT NULL
                            )
                        """.trimIndent())
                    }
                })
                .addCallback(object : Callback() {
                    override fun onCreate(db: SupportSQLiteDatabase) {
                        super.onCreate(db)
                        initNoteCategoryData(db, context)
                        insertJournalSample(db, context)
                    }
                }).setJournalMode(JournalMode.TRUNCATE) // 禁用WAL模式(测试用)
                .build()
        }

        /**
         * 填充 Category 表的数据，初始创建完表调用
         */
        private fun initNoteCategoryData(db: SupportSQLiteDatabase, context: Context) {
            val categories: MutableList<Pair<String, Int>> = ArrayList()
            categories.add(
                Pair(
                    context.getString(R.string.database_preset_category_none),
                    CategoryColors.NONE_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(com.tcl.ai.note.resources.R.string.title_category_study),
                    CategoryColors.GREEN_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(com.tcl.ai.note.resources.R.string.title_category_pet),
                    CategoryColors.YELLOW_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(com.tcl.ai.note.resources.R.string.title_category_work),
                    CategoryColors.BLUE_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(com.tcl.ai.note.resources.R.string.title_category_life),
                    CategoryColors.PINK_COLOR
                )
            )
            categories.add(
                Pair(
                    context.getString(com.tcl.ai.note.resources.R.string.title_category_travel),
                    CategoryColors.PURPLE_COLOR
                )
            )
            for (i in categories.indices) {
                val category = categories[i]
                val currentTime = System.currentTimeMillis() + i
                val values = ContentValues()
                values.put("name", category.first)
                values.put("createTime", currentTime)
                values.put("modifyTime", currentTime)
                values.put("colorIndex", category.second)
                values.put("isRename", false)
                db.insert(DBConst.TABLE_NAME_CATEGORIES, SQLiteDatabase.CONFLICT_REPLACE, values)
            }
        }

        private fun insertJournalSample(
            db: SupportSQLiteDatabase,
            context: Context
        ) {
            val values = ContentValues()
            values.put("title", String.format(context.getString(com.tcl.ai.note.resources.R.string.default_journal_title),
                context.getString(com.tcl.ai.note.resources.R.string.title_category_travel)))
            values.put("coverId", 1)
            values.put("categoryId", 6)
            values.put("createTime", System.currentTimeMillis())
            values.put("modifyTime", System.currentTimeMillis())
            values.put("lastViewPage", 0)
            val journalId = db.insert(DBConst.TABLE_NAME_JOURNAL, SQLiteDatabase.CONFLICT_REPLACE, values)
            Logger.d("JournalDatabase", "insertJournalSample: journalId = $journalId, values = $values")
            insertJournalContent(
                db,
                context,
                journalId
            )
        }

        private fun insertJournalContent(
            db: SupportSQLiteDatabase,
            context: Context,
            journalId: Long = 1
        ) {
            val thumbnailDir = File(context.filesDir, "journalContentThumbnails/$journalId")
            copySampleAssetsToExternal("journalContentThumbnails", thumbnailDir)
            val saveDir = File(context.filesDir, "journalContentEnts/$journalId")
            copySampleAssetsToExternal("sampleEntFiles", saveDir)
            val pageInfoConvertor = PageInfoConvertor()
            val values = ContentValues()
            values.put("contentId", 1)
            values.put("journalId", journalId)
            values.put("lastPageIndex", 0)
            values.put("totalPageIndex", 9)
            val pageInfos = mutableListOf<PageInfo>()
            for (i in 0 until 9) {
                pageInfos.add(
                    PageInfo(
                        pageId = i,
                        bitmapFilePath = File(thumbnailDir, "thumbnail_$i.png").absolutePath,
                        entFilePath = File(saveDir, "ent_$i.ent").absolutePath,
                    )
                )
            }
            values.put("pageInfos", pageInfoConvertor.pageInfoListToByteArray(pageInfos))
            db.insert(DBConst.TABLE_NAME_JOURNAL_CONTENT, SQLiteDatabase.CONFLICT_REPLACE, values)
            Logger.d("JournalDatabase", "insertJournalContent: journalId = $journalId, values = $values")
        }

    }
}