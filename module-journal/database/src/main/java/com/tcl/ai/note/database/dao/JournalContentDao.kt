package com.tcl.ai.note.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.TypeConverters
import androidx.room.Update
import com.tcl.ai.note.database.convertor.PageInfoConvertor
import com.tcl.ai.note.database.entity.DBConst
import com.tcl.ai.note.database.entity.JournalContent

@Dao
@TypeConverters(PageInfoConvertor::class)
interface JournalContentDao {
    @Query("SELECT * FROM ${DBConst.TABLE_NAME_JOURNAL_CONTENT} WHERE journalId = :journalId")
    fun getJournalContentById(journalId: Long): JournalContent?

    @Insert
    fun insert(journalContent: JournalContent): Long

    @Update
    fun update(journalContent: JournalContent): Int

    @Query("DELETE FROM ${DBConst.TABLE_NAME_JOURNAL_CONTENT} WHERE journalId = :journalId")
    fun delete(journalId: Long): Int

    @Query("DELETE FROM ${DBConst.TABLE_NAME_JOURNAL_CONTENT} WHERE journalId IN (:journalIds)")
    fun deleteContents(journalIds: List<Long>): Int
}