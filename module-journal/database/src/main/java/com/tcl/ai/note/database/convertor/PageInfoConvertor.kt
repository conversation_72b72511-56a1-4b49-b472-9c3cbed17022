package com.tcl.ai.note.database.convertor

import androidx.room.TypeConverter
import com.tcl.ai.note.database.entity.PageInfo
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromByteArray
import kotlinx.serialization.encodeToByteArray
import kotlinx.serialization.protobuf.ProtoBuf

@OptIn(ExperimentalSerializationApi::class)
class PageInfoConvertor {
    private val protoFormat = ProtoBuf

    @TypeConverter
    fun pageInfoListToByteArray(strokes: List<PageInfo>) =
        protoFormat.encodeToByteArray(strokes)

    @TypeConverter
    fun byteArrayToPageInfoList(byteArray: ByteArray) =
        protoFormat.decodeFromByteArray<List<PageInfo>>(byteArray)
}