package com.tcl.ai.note.database

import android.content.Context
import com.tcl.ai.note.database.dao.AlbumDao
import com.tcl.ai.note.database.dao.CategoryDao
import com.tcl.ai.note.database.dao.ImageDao
import com.tcl.ai.note.database.dao.JournalContentDao
import com.tcl.ai.note.database.dao.JournalDao
import com.tcl.ai.note.database.db.JournalDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class DatabaseModule {

    @Provides
    fun provideNoteRoomDao(noteDatabase: JournalDatabase): JournalDao {
        return noteDatabase.noteDao()
    }

    /*@Provides
    fun provideContentRoomDao(noteDatabase: NoteDatabase): ContentDao {
        return noteDatabase.contentDao()
    }*/

    @Provides
    fun provideCategoryRoomDao(noteDatabase: JournalDatabase): CategoryDao {
        return noteDatabase.categoryDao()
    }

    @Provides
    fun provideImageRoomDao(noteDatabase: JournalDatabase): ImageDao {
        return noteDatabase.imageDao()
    }

    @Provides
    fun providerAlbumRoomDao(noteDatabase: JournalDatabase): AlbumDao {
        return noteDatabase.albumDao()
    }

    @Provides
    fun provideJournalContentRoomDao(noteDatabase: JournalDatabase): JournalContentDao {
        return noteDatabase.journalContentDao()
    }

    @Provides
    @Singleton
    fun provideNoteDatabase(@ApplicationContext appContext: Context): JournalDatabase {
        return JournalDatabase.getInstance(appContext)
//        return Room.databaseBuilder(
//            appContext,
//            NoteDatabase::class.java,
//            DBConst.DB_NAME
//        ).build()
    }
}