package com.tcl.ai.note.database.repository

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.database.db.JournalDatabase
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.withContext

/**
 * 首页数据仓库 包含笔记和分类的数据仓库
 * 只引入必要的接口
 */
object HomeRepository {
    private const val TAG = "HomeRepository"
    private val noteDao = JournalDatabase.getInstance(GlobalContext.instance).noteDao()
    private val categoryDao = JournalDatabase.getInstance(GlobalContext.instance).categoryDao()

    // 获取所有Note数据
    fun getAllJournals(): Flow<List<Journal>> {
        Logger.d(TAG, "getAllNote - Starting database query")
        return noteDao.getAllJournals()
            .onStart { Logger.d(TAG, "Database query flow started: ${System.currentTimeMillis()}") }
            .onEach { notes ->
                Logger.d(TAG, "Database query completed: ${System.currentTimeMillis()}, found ${notes.size} records")
            }
    }

    /**
     * 获取所有分类名称，同时监听分类和笔记总数量的变化
     * 返回 Pair<List<NoteCategory>, Int>
     */
    fun getAllCategoriesList(): Flow<Pair<List<JournalCategory>, Int>> {
        return combine(
            categoryDao.getAllCategoriesFlow(),
            noteDao.getNoteCountFlow() // 监听笔记表的变化
        ) { categories, allNoteCount ->
            // 当分类表或笔记表发生变化时，都会重新计算
            categories.map { category ->
                // 计算每个分类中的Note数量
                category.noteCounts = calculateNoteCounts(category.categoryId)
                // 确定分类icon
                //category.icon = determineIcon(category)
                // 确定显示名称
                category.name = determineName(category)
                category
            } to allNoteCount
        }
    }
    // 计算每个分类中的Note数量
    suspend fun calculateNoteCounts(categoryId: Long): Int {
        // 这里是计算 noteCounts 的逻辑，例如从笔记表中统计笔记数量。
        return noteDao.getNotesCountByCategoryId(categoryId)
    }

    // 确定分类icon
    private fun determineIcon(category: JournalCategory): Int {
        // 这里是确定图标的逻辑，可以根据分类的某些属性或预置逻辑设置图标。
        return when (category.colorIndex) {
            CategoryColors.NONE_COLOR -> R.drawable.ic_category_uncategorised // 未分类
            CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
            CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
            CategoryColors.PINK_COLOR -> R.drawable.ic_category_pink
            CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
            CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
            CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
            else -> R.drawable.ic_all_notes
        }
    }
    // 重新设置显示名称
    private fun determineName(category: JournalCategory):String {
        return when {
            category.isRename -> category.name
            else -> when (category.categoryId) {
                1L -> GlobalContext.Companion.instance.getString(R.string.database_preset_category_none)
                2L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_study)
                3L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_pet)
                4L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_work)
                5L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_life)
                6L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_travel)
                else -> category.name
            }
        }
    }
    /**
     * 新增category
     */
    suspend fun addCategory(category: JournalCategory): Long = withContext(Dispatchers.IO) {
        categoryDao.insert(category)
    }
    /**
     * 删除某个分类下的Note数据
     */
    suspend fun deleteNotesByCategoryId(categoryId: Long):Int = withContext(Dispatchers.IO) {
        Logger.d(TAG, "deleteNotesByCategoryId, categoryId: $categoryId")
        noteDao.deleteNotesByCategoryId(categoryId)
    }

    /**
     * 删除某个分类下的Note数据
     */
    suspend fun updateCategoryId(categoryId: Long,newCategoryId:Long): Int =
        withContext(Dispatchers.IO) {
            Logger.d(
                TAG,
                "updateCategoryId, categoryId: $categoryId, newCategoryId: $newCategoryId"
            )
            noteDao.updateCategoryId(categoryId, newCategoryId)
        }
    /**
     * 删除指定的Notes
     */
    suspend fun deleteNotes(noteIds: List<Long>):Int = withContext(Dispatchers.IO) {
        Logger.d(TAG, "deleteNotes: $noteIds")
        noteDao.deleteNotes(noteIds)
    }
    /**
     * 删除一个Category
     */
    suspend fun deleteCategory(category: JournalCategory):Int = withContext(Dispatchers.IO) {
        categoryDao.delete(category)
    }
    /**
     * 更Category
     */
    suspend fun updateCategory(category: JournalCategory): Long = withContext(Dispatchers.IO) {
        categoryDao.update(category)
        category.categoryId
    }

    /**
     * 获取一条Category
     */
    suspend fun getCategory(categoryId: Long): JournalCategory? = withContext(Dispatchers.IO) {
        val category = categoryDao.getCategoryById(categoryId) ?: return@withContext null
        category.apply {
            //icon = determineIcon(this)
            name = determineName(this)
        }
        return@withContext category
    }
    /**
     * 更新多条指定Note的 categoryId
     */
    suspend fun updateNotesCategoryId(noteIds: List<Long>, newCategoryId: Long): Int = withContext(Dispatchers.IO){
        Logger.d(TAG, "updateNotesCategoryId, noteIds: $noteIds, newCategoryId: $newCategoryId")
        noteDao.updateNotesCategoryId(noteIds,newCategoryId)
    }

    suspend fun getDefaultJournalCategory(): JournalCategory  = withContext(Dispatchers.IO) {
        for (categoryId in 6L downTo 2L) {
            categoryDao.getCategoryById(categoryId)?.let { category ->
                return@withContext category
            }
        }
        return@withContext JournalCategory(categoryId = 1L, name = GlobalContext.instance.getString(R.string.database_preset_category_none), colorIndex = CategoryColors.NONE_COLOR)
    }
}