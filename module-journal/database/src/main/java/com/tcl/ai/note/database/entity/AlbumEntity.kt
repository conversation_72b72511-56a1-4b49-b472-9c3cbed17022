package com.tcl.ai.note.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = DBConst.TABLE_NAME_ALBUM)
data class Album(
    @PrimaryKey
    @ColumnInfo(name = AlbumEntity.ALBUM_ID) val albumId: String,
    @ColumnInfo(name = AlbumEntity.TIMESTAMP) val timestamp: Long,
    @ColumnInfo(name = AlbumEntity.DATE) val date: String,
    @ColumnInfo(name = AlbumEntity.TIME_SLOT) val timeSlot: String,
    @ColumnInfo(name = AlbumEntity.LOCATION) val location: String?,
    @ColumnInfo(name = AlbumEntity.SIZE) val size: Int,
    @ColumnInfo(name = AlbumEntity.ALBUM_NAME) val albumName: String?,
)

object AlbumEntity {
    const val ALBUM_ID = "album_id"
    const val TIMESTAMP = "timestamp"
    const val DATE = "date"
    const val TIME_SLOT = "time_slot"
    const val LOCATION = "location"
    const val SIZE = "size"
    const val ALBUM_NAME = "albumName"
}