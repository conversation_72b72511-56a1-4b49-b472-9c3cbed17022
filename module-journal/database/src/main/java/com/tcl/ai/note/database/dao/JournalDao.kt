package com.tcl.ai.note.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import com.tcl.ai.note.database.entity.DBConst
import com.tcl.ai.note.database.entity.Journal
import kotlinx.coroutines.flow.Flow

@Dao
interface JournalDao {

    @Query("SELECT journalId, coverId, title, categoryId,createTime,modifyTime,lastViewPage FROM journal order by CASE WHEN journalId = 1 THEN 0 ELSE 1 END, CASE WHEN journalId = 1 THEN NULL ELSE createTime END DESC")
    //@Query("SELECT journalId, coverId, title, categoryId,createTime,modifyTime,lastViewPage FROM journal order by createTime DESC")
    fun getAllJournals(): Flow<List<Journal>>

    @Query("SELECT journalId,coverId,title,categoryId,createTime,modifyTime,lastViewPage FROM journal order by CASE WHEN journalId = 1 THEN 0 ELSE 1 END, CASE WHEN journalId = 1 THEN NULL ELSE modifyTime END DESC")
    fun getAllNotesByModifyTime(): List<Journal>

    // 分页查询
    @Query(
        """
        SELECT journalId, coverId, title, categoryId,createTime,modifyTime,lastViewPage
        FROM journal 
        ORDER BY CASE WHEN journalId = 1 THEN 0 ELSE 1 END, CASE WHEN journalId = 1 THEN NULL ELSE modifyTime END DESC 
        LIMIT :pageSize OFFSET :offset
    """
    )
    suspend fun getAllNotesByModifyTimePaged(offset: Int, pageSize: Int): List<Journal>

    @Query("SELECT COUNT(*) FROM journal WHERE categoryId = :categoryId")
    suspend fun getNotesCountByCategoryId(categoryId: Long): Int

    // 分页查询
    @Query(
        """
        SELECT journalId, coverId, title, categoryId,createTime,modifyTime,lastViewPage
        FROM journal 
        ORDER BY CASE WHEN journalId = 1 THEN 0 ELSE 1 END, CASE WHEN journalId = 1 THEN NULL ELSE createTime END DESC 
        LIMIT :pageSize OFFSET :offset
    """
    )
    suspend fun getAllNotesPaged(offset: Int, pageSize: Int): List<Journal>

    @Query(
        """
        SELECT journalId, coverId, title, categoryId,createTime,modifyTime,lastViewPage
        FROM journal 
        WHERE categoryId = :categoryId 
        ORDER BY CASE WHEN journalId = 1 THEN 0 ELSE 1 END, CASE WHEN journalId = 1 THEN NULL ELSE createTime END DESC 
        LIMIT :pageSize OFFSET :offset
    """
    )
    suspend fun getNotesByCategoryIdPaged(
        categoryId: Long,
        offset: Int,
        pageSize: Int
    ): List<Journal>


    @Query("SELECT journalId, coverId, title, categoryId,createTime,modifyTime,lastViewPage FROM journal where categoryId = :categoryId order by CASE WHEN journalId = 1 THEN 0 ELSE 1 END, CASE WHEN journalId = 1 THEN NULL ELSE createTime END DESC")
    fun getNotesByCategoryId(categoryId: Long): List<Journal>

    @Query("SELECT journalId, coverId, title, categoryId,createTime,modifyTime,lastViewPage FROM journal where categoryId = :categoryId order by CASE WHEN journalId = 1 THEN 0 ELSE 1 END, CASE WHEN journalId = 1 THEN NULL ELSE modifyTime END DESC")
    fun getNotesByCategoryIdByModifyTime(categoryId: Long): List<Journal>

    @Query(
        """
        SELECT journalId, coverId, title, categoryId,createTime,modifyTime,lastViewPage
        FROM journal 
        WHERE categoryId = :categoryId 
        ORDER BY CASE WHEN journalId = 1 THEN 0 ELSE 1 END, CASE WHEN journalId = 1 THEN NULL ELSE modifyTime END DESC 
        LIMIT :pageSize OFFSET :offset
    """
    )
    suspend fun getNotesByCategoryIdByModifyTimePaged(
        categoryId: Long,
        offset: Int,
        pageSize: Int
    ): List<Journal>

    @Query("Delete FROM journal where categoryId = :categoryId")
    fun deleteNotesByCategoryId(categoryId: Long): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_JOURNAL} SET categoryId = :newCategoryId WHERE categoryId = :oldCategoryId")
    suspend fun updateCategoryId(oldCategoryId: Long, newCategoryId: Long): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_JOURNAL} SET categoryId = :newCategoryId WHERE journalId IN (:noteIds)")
    suspend fun updateNotesCategoryId(noteIds: List<Long>, newCategoryId: Long): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_JOURNAL} SET categoryId = :categoryId WHERE journalId =:noteId")
    suspend fun updateNoteCategoryId(noteId: Long, categoryId: Long): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_JOURNAL} SET title = :title, modifyTime = :timeMillis WHERE journalId =:journalId")
    suspend fun updateJournalTitle(journalId: Long, title: String, timeMillis: Long = System.currentTimeMillis()): Int

    @Query("UPDATE ${DBConst.TABLE_NAME_JOURNAL} SET modifyTime = :timeMillis WHERE journalId =:journalId")
    suspend fun updateJournalModifyTime(journalId: Long, timeMillis: Long = System.currentTimeMillis())

    @Insert
    suspend fun insert(note: Journal): Long

    @Update
    suspend fun update(note: Journal): Int

    @Delete
    suspend fun delete(note: Journal): Int

    @Query("DELETE FROM ${DBConst.TABLE_NAME_JOURNAL} WHERE journalId = :noteId")
    suspend fun deleteOneNote(noteId: Long): Int

    @Query("DELETE FROM ${DBConst.TABLE_NAME_JOURNAL} WHERE journalId IN (:noteIds)")
    suspend fun deleteNotes(noteIds: List<Long>): Int

    @Query("SELECT * FROM journal WHERE journalId = :noteId")
    suspend fun getNote(noteId: Long): Journal?

    // 新增计数查询
    @Query("SELECT COUNT(*) FROM journal")
    suspend fun getNoteCount(): Int

    /**
     * 搜索操作
     */
    @Query("SELECT * FROM ${DBConst.TABLE_NAME_JOURNAL} WHERE title LIKE '%' || :query || '%'")
    suspend fun searchJournals(query: String): List<Journal>

    /**
     * 在某个分类里面进行搜索操作
     */
    @Query(
        """
        SELECT * FROM ${DBConst.TABLE_NAME_JOURNAL}
        WHERE categoryId = :categoryId
        AND (title LIKE '%' || :query || '%')
    """
    )
    suspend fun searchJournalsByCategoryAndQuery(query: String, categoryId: Long): List<Journal>

    @Query("SELECT COUNT(*) FROM journal")
    fun getNoteCountFlow(): Flow<Int>

}