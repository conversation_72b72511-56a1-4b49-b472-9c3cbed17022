package com.tcl.ai.note.database.repository

import com.tcl.ai.note.database.IoDispatcher
import com.tcl.ai.note.database.dao.JournalDao
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 临时创建，用于Note数据库创建的参考
 *
 */
class JournalRepository @Inject constructor(
    private val dao: JournalDao,
    @IoDispatcher private val dispatcher: CoroutineDispatcher // 添加限定符
) {

    fun getAllJournalsFlow(): Flow<List<Journal>> {
        return dao.getAllJournals()
    }

    /**
     * 获取所有Note(默认按照创建时间时间)
     */
    suspend fun getAllNotesPaged(page: Int, pageSize: Int): Pair<List<Journal>, Boolean> {
        val offset = (page - 1) * pageSize
        val items = dao.getAllNotesPaged(offset, pageSize)
        val hasMore = items.size == pageSize
        return Pair(items, hasMore)
    }

    /**
     * 获取所有Note(按照修改时间)
     */
    suspend fun getAllNotesByModifyTimePaged(page: Int, pageSize: Int): Pair<List<Journal>, Boolean> =
        withContext(dispatcher) {
            val offset = (page - 1) * pageSize
            val items = dao.getAllNotesByModifyTimePaged(offset, pageSize)
            val hasMore = items.size == pageSize
            return@withContext Pair(items, hasMore)
        }




    /**
     * 获取一个分类下的Note数据(默认按照创建时间)
     */
    suspend fun getNotesByCategoryIdPaged(categoryId: Long, page: Int, pageSize: Int): Pair<List<Journal>, Boolean> =
        withContext(dispatcher) {
            val offset = (page - 1) * pageSize
            val items = dao.getNotesByCategoryIdPaged(categoryId, offset, pageSize)
            val hasMore = items.size == pageSize
            return@withContext Pair(items, hasMore)
        }

    /**
     * 获取一个分类下的Note数据(按照修改时间)
     */
    suspend fun getNotesByCategoryIdByModifyTimePaged(categoryId:Long, page: Int, pageSize: Int):Pair<List<Journal>, Boolean> =
        withContext(dispatcher) {
            val offset = (page - 1) * pageSize
            val items = dao.getNotesByCategoryIdByModifyTimePaged(categoryId, offset, pageSize)
            val hasMore = items.size == pageSize
            return@withContext Pair(items, hasMore)
        }

    /**
     * 删除某个分类下的Note数据
     */
    suspend fun deleteNotesByCategoryId(categoryId: Long):Int = withContext(dispatcher) {
        dao.deleteNotesByCategoryId(categoryId)
    }

    /**
     * 删除某个分类下的Note数据
     */
    suspend fun updateCategoryId(categoryId: Long,newCategoryId:Long): Int =
        withContext(dispatcher) {
            dao.updateCategoryId(categoryId, newCategoryId)
        }

    /**
     * 更新多条指定Note的 categoryId
     */
    suspend fun updateNotesCategoryId(noteIds: List<Long>, newCategoryId: Long): Int =
        withContext(dispatcher) {
            dao.updateNotesCategoryId(noteIds, newCategoryId)
        }


    /**
     * 更新单条指定 Note 的 categoryId
     */
    suspend fun updateNoteCategoryId(noteId: Long, categoryId: Long): Int =
        withContext(dispatcher) {
            dao.updateNoteCategoryId(noteId, categoryId)
        }

    /**
     * 更新单条指定日记 的 title
     */
    suspend fun updateJournalTitle(journalId: Long, title: String): Int =
        withContext(dispatcher) {
            dao.updateJournalTitle(journalId, title)
        }

    suspend fun updateJournalModifyTime(journalId: Long) =
        withContext(dispatcher) {
            dao.updateJournalModifyTime(journalId)
            Logger.d("JournalRepository", "updateJournalModifyTime journalId: $journalId")
        }


    /**
     * 搜索笔记数据
     */
    suspend fun searchJournals(text: String): List<Journal> = withContext(dispatcher) {
        val journals = dao.searchJournals(text)
        return@withContext journals
    }

    /**
     * 搜索笔记数据
     */
    suspend fun searchJournalsByCategoryAndQuery(text: String,categoryId: Long): List<Journal> = withContext(dispatcher) {
        val journals = dao.searchJournalsByCategoryAndQuery(text,categoryId)
        return@withContext journals
    }

    /**
     * 新增note
     */
    suspend fun insertJournal(note : Journal): Long = withContext(dispatcher) {
        dao.insert(note)
    }

    /**
     * 更note，这里代码还有缺陷，只作业务参考，
     */
    suspend fun updateNote(note : Journal): Long = withContext(dispatcher) {
        dao.update(note)
        note.journalId
    }

    /**
     * 删除一条Note
     */
    suspend fun deleteNote(noteId :Long):Int = withContext(dispatcher) {
        dao.deleteOneNote(noteId)
    }


    /**
     * 删除指定的Notes
     */
    suspend fun deleteNotes(noteIds: List<Long>):Int = withContext(dispatcher) {
        dao.deleteNotes(noteIds)
    }

    /**
     * 获取一条Note
     */
    suspend fun getNote(noteId: Long): Journal? = withContext(dispatcher) {
        dao.getNote(noteId)
    }

    // 新增获取笔记数量的方法
    suspend fun getNoteCount(): Int = withContext(dispatcher) {
        return@withContext dao.getNoteCount()
    }
}