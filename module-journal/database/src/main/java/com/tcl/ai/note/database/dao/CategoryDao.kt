package com.tcl.ai.note.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import com.tcl.ai.note.database.entity.JournalCategory
import kotlinx.coroutines.flow.Flow

@Dao
interface CategoryDao {
    @Query("SELECT * FROM journal_categories ORDER BY modifyTime DESC")
    fun getAllCategories(): List<JournalCategory>

    @Query("SELECT * FROM journal_categories WHERE categoryId = :categoryId")
    fun getCategoryById(categoryId: Long): JournalCategory?

    @Insert
    fun insert(category: JournalCategory): Long

    @Update
    fun update(category: JournalCategory): Int

    @Delete
    fun delete(category: JournalCategory): Int

    @Query("SELECT * FROM journal_categories ORDER BY modifyTime DESC")
    fun getAllCategoriesFlow(): Flow<List<JournalCategory>>
}