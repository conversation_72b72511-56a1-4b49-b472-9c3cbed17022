package com.tcl.ai.note.database.convertor

import androidx.room.TypeConverter
import java.nio.ByteBuffer

object ImageEmbeddingConverter {

    // FloatArray 转 ByteArray，用于存入 BLOB
    @TypeConverter
    @JvmStatic
    fun fromFloatArray(array: FloatArray): ByteArray {
        val byteBuffer = ByteBuffer.allocate(array.size * 4)
        array.forEach { byteBuffer.putFloat(it) }
        return byteBuffer.array()
    }

    // ByteArray 转 FloatArray，从 BLOB 读出
    @TypeConverter
    @JvmStatic
    fun toFloatArray(bytes: ByteArray): FloatArray {
        val floatBuffer = ByteBuffer.wrap(bytes).asFloatBuffer()
        val floatArray = FloatArray(floatBuffer.limit())
        floatBuffer.get(floatArray)
        return floatArray
    }
}