package com.tcl.ai.note.database.repository

import androidx.room.withTransaction
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.dao.AlbumDao
import com.tcl.ai.note.database.dao.ImageDao
import com.tcl.ai.note.database.dao.ImageEmbeddingDao
import com.tcl.ai.note.database.db.JournalDatabase
import com.tcl.ai.note.database.entity.Album
import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.database.entity.ImageEmbedding
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

class InspirationRepository {

    private val journalDatabase = JournalDatabase.Companion.getInstance(GlobalContext.Companion.instance)

    private val imageDao: ImageDao = journalDatabase.imageDao()
    private val albumDao: AlbumDao = journalDatabase.albumDao()
    private val imageEmbeddingDao: ImageEmbeddingDao = journalDatabase.imageEmbeddingDao()

    private val dispatcher: CoroutineDispatcher = Dispatchers.IO

    suspend fun queryAllImages(): List<Image> = withContext(dispatcher) {
        imageDao.queryImageInfo()
    }

    suspend fun queryImagesByAlbumId(albumId: String): List<Image> = withContext(dispatcher) {
        imageDao.queryImagesByAlbumId(albumId)
    }

    suspend fun saveImage(image: Image) = withContext(dispatcher) {
        imageDao.saveImageInfo(image)
    }

    suspend fun saveImages(images: List<Image>) = withContext(dispatcher) {
        imageDao.saveImageInfos(images)
    }

    suspend fun updateImage(image: Image) = withContext(dispatcher) {
        imageDao.updateImageInfo(image)
    }

    suspend fun updateImages(images: List<Image>) = withContext(dispatcher) {
        imageDao.updateImageInfos(images)
    }

    suspend fun deleteImages(images: List<Image>) = withContext(dispatcher) {
        imageDao.deleteImages(images.map { it.imageId })
    }

    fun queryAllAlums(): Flow<List<Album>> {
        return albumDao.queryAlbums()
    }

    suspend fun saveAlbums(albums: List<Album>) = withContext(dispatcher) {
        albumDao.saveAlbums(albums)
    }

    suspend fun updateAlbum(album: Album) = withContext(dispatcher) {
        albumDao.updateAlbum(album)
    }

    suspend fun deleteAlbums(albumIds: List<String>) = withContext(dispatcher) {
        albumDao.deleteAlbums(albumIds)
    }

    suspend fun clearAndSaveAlbums(albums: List<Album>) = withContext(dispatcher) {
        journalDatabase.withTransaction {
            albumDao.deleteAllAlbums()
            albumDao.saveAlbums(albums)
        }
    }

    suspend fun queryImageEmbedding(): List<ImageEmbedding> = withContext(dispatcher) {
        imageEmbeddingDao.queryImageEmbedding()
    }

    suspend fun saveImageEmbeddings(imageEmbeddings: List<ImageEmbedding>) = withContext(dispatcher) {
        imageEmbeddingDao.saveImageEmbeddings(imageEmbeddings)
    }
}