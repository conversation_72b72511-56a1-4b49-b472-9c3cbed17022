package com.tcl.ai.note.database.repository

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.database.IoDispatcher
import com.tcl.ai.note.database.dao.CategoryDao
import com.tcl.ai.note.database.dao.JournalDao
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.utils.CategoryColors
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 临时创建，用于Category数据库创建的参考
 *
 */
class CategoryRepository @Inject constructor(
    private val dao: CategoryDao,
    private val noteDao: JournalDao,
    @IoDispatcher private val dispatcher: CoroutineDispatcher // 添加限定符
) {
    /**
     * 获取所有分类数据
     */
    suspend fun getAllCategories(): List<JournalCategory> = withContext(dispatcher) {
        try {
            val categories = dao.getAllCategories()
            categories.forEach { category ->
                category.noteCounts = calculateNoteCounts(category.categoryId)
                category.name = determineName(category)
            }
            if (categories.size >= 2) {
                return@withContext categories.filter { it.categoryId > 1L || it.noteCounts > 0 } // 过滤掉ID为1并且没有回忆集的分类(即未分类在没有回忆集的情况下不显示)
            } else {
                return@withContext categories
            }

        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * 获取所有分类名称，同时监听分类和笔记总数量的变化
     * 返回 Pair<List<NoteCategory>, Int>
     */
    fun getAllCategoriesList(): Flow<Pair<List<JournalCategory>, Int>> {
        return combine(
            dao.getAllCategoriesFlow(),
            noteDao.getNoteCountFlow() // 监听笔记表的变化
        ) { categories, allNoteCount ->
            // 当分类表或笔记表发生变化时，都会重新计算
            categories.map { category ->
                // 计算每个分类中的Note数量
                category.noteCounts = calculateNoteCounts(category.categoryId)
                // 确定分类icon
                //category.icon = determineIcon(category)
                // 确定显示名称
                category.name = determineName(category)
                category
            } to allNoteCount
        }
    }

    // 计算每个分类中的Note数量
    private suspend fun calculateNoteCounts(categoryId: Long): Int {
        // 这里是计算 noteCounts 的逻辑，例如从笔记表中统计笔记数量。
        return noteDao.getNotesCountByCategoryId(categoryId)
    }

    // 重新设置显示名称
    private fun determineName(category: JournalCategory):String{
        return when {
            category.isRename -> category.name
            else -> when (category.categoryId) {
                1L -> GlobalContext.Companion.instance.getString(R.string.database_preset_category_none)
                2L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_study)
                3L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_pet)
                4L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_work)
                5L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_life)
                6L -> GlobalContext.Companion.instance.getString(com.tcl.ai.note.resources.R.string.title_category_travel)
                else -> category.name
            }
        }
    }

    // 确定分类icon
    private fun determineIcon(category: JournalCategory): Int {
        // 这里是确定图标的逻辑，可以根据分类的某些属性或预置逻辑设置图标。
        return when (category.colorIndex) {
            CategoryColors.NONE_COLOR -> R.drawable.ic_category_uncategorised // 未分类
            CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
            CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
            CategoryColors.PINK_COLOR -> R.drawable.ic_category_pink
            CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
            CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
            CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
            else -> R.drawable.ic_all_notes
        }
    }


    /**
     * 新增category
     */
    suspend fun addCategory(category: JournalCategory): Long = withContext(dispatcher) {
        dao.insert(category)
    }

    /**
     * 更Category
     */
    suspend fun updateCategory(category: JournalCategory): Long = withContext(dispatcher) {
        dao.update(category)
        category.categoryId
    }

    /**
     * 获取一条Category
     */
    suspend fun getCategory(categoryId: Long): JournalCategory? = withContext(dispatcher) {
        val category = dao.getCategoryById(categoryId)
        category?.apply {
            name = determineName(category)
        }
        return@withContext category
    }

    /**
     * 删除一个Category
     */
    suspend fun deleteCategory(category: JournalCategory):Int = withContext(dispatcher) {
        dao.delete(category)
    }


}