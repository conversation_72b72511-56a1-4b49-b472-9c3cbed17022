package com.tcl.ai.note.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.tcl.ai.note.database.convertor.ImageEmbeddingConverter

@Entity(tableName = DBConst.TABLE_NAME_IMAGE_EMBEDDING)
@TypeConverters(ImageEmbeddingConverter::class)
data class ImageEmbedding(
    @PrimaryKey
    @ColumnInfo(name = ImageEmbeddingEntity.IMAGE_ID) val imageId: String,
    @ColumnInfo(name = ImageEmbeddingEntity.FLOAT_EMBEDDING) val floatEmbedding: FloatArray = FloatArray(0),
    @ColumnInfo(name = ImageEmbeddingEntity.QUANTIZED_EMBEDDING) var quantizedEmbedding: ByteArray = ByteArray(0),
    @ColumnInfo(name = ImageEmbeddingEntity.HEAD_INDEX) var headIndex: Int = 0,
    @ColumnInfo(name = ImageEmbeddingEntity.HEAD_NAME) var headName: String,
)

object ImageEmbeddingEntity {
    const val IMAGE_ID = "image_id"
    const val FLOAT_EMBEDDING = "float_embedding"
    const val QUANTIZED_EMBEDDING = "quantized_embedding"
    const val HEAD_INDEX = "head_index"
    const val HEAD_NAME = "head_name"
}