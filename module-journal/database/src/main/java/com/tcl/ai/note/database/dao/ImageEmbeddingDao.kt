package com.tcl.ai.note.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.tcl.ai.note.database.entity.ImageEmbedding

@Dao
interface ImageEmbeddingDao {

    @Query("select * from image_embedding")
    suspend fun queryImageEmbedding(): List<ImageEmbedding>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveImageEmbeddings(images: List<ImageEmbedding>)
}