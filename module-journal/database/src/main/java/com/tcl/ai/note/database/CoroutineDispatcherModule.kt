package com.tcl.ai.note.database

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import javax.inject.Qualifier

// CoroutineDispatcherModule.kt
@Module
@InstallIn(SingletonComponent::class)
object CoroutineDispatcherModule {
    @Provides
    @IoDispatcher
    fun provideIoDispatcher(): CoroutineDispatcher = Dispatchers.IO

    @Provides
    @ComputationalDispatcher
    fun providerComputationalDispatcher(): CoroutineDispatcher = Dispatchers.Default
}

// 定义限定符
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class IoDispatcher


@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ComputationalDispatcher
