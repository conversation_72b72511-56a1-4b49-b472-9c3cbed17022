package com.tcl.ai.note.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.tcl.ai.note.database.entity.Album
import kotlinx.coroutines.flow.Flow

@Dao
interface AlbumDao {
    @Query("select * from album")
    fun queryAlbums(): Flow<List<Album>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveAlbum(album: Album)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveAlbums(albums: List<Album>)

    @Update
    suspend fun updateAlbum(album: Album)

    @Query("delete from album where album_id = :albumId")
    suspend fun deleteAlbum(albumId: String)

    @Query("delete from album where album_id in (:albumIds)")
    suspend fun deleteAlbums(albumIds: List<String>)

    @Query("DELETE FROM album")
    suspend fun deleteAllAlbums()
}