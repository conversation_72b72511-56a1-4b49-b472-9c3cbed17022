package com.tcl.ai.note.database.entity

import androidx.annotation.Keep
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.tcl.ai.note.database.convertor.PageInfoConvertor
import kotlinx.serialization.Serializable

// 数据库实现（使用Room）
@Keep
@Entity(tableName = DBConst.TABLE_NAME_JOURNAL)
data class Journal(
    @PrimaryKey(autoGenerate = true)
    val journalId: Long = 0,
    //@PrimaryKey val id: String = UUID.randomUUID().toString(),
    var title: String = "",
    val coverId: Long = 0,
    //var content: String = "", // 完整内容字段
    var categoryId: Long = 5, // 未分类
    var createTime: Long? = null,
    var modifyTime: Long? = null,
    var lastViewPage: Int = 0,
)

@Keep
@Entity(tableName = DBConst.TABLE_NAME_CATEGORIES)
data class JournalCategory(
    @PrimaryKey(autoGenerate = true)
    val categoryId: Long = 0,
    var name: String = "",
    var colorIndex: Int = 0,
    var isRename: Boolean = false, // name是否更改过
    var createTime: Long? = null,
    var modifyTime: Long? = null
) {
    @Ignore
    var noteCounts: Int = 0
}

@Keep
@Entity(tableName = DBConst.TABLE_NAME_JOURNAL_CONTENT)
@TypeConverters(PageInfoConvertor::class)
data class JournalContent(
    @PrimaryKey(autoGenerate = true)
    val contentId: Long = 0,
    val journalId: Long = 0,
    val lastPageIndex: Int = 0, // 上次查看的页面下标
    val totalPageIndex: Int = 0,
    val pageInfos: List<PageInfo> = emptyList(), // page列表
)

@Keep
@Serializable
data class PageInfo(
    val pageId: Int = 0,
    val bgColorResId: Int? = null,
    val bgImageResId: Int? = null,
    val entFilePath: String = "", // ent文件路径
    val bitmapFilePath: String = "", // 图片文件路径
)