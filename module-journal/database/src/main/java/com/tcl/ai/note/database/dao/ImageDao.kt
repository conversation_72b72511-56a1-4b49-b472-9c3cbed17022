package com.tcl.ai.note.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.tcl.ai.note.database.entity.Image

@Dao
interface ImageDao {

    @Query("select * from image")
    suspend fun queryImageInfo(): List<Image>

    @Query("select * from image where album_id = :albumId")
    suspend fun queryImagesByAlbumId(albumId: String): List<Image>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveImageInfo(image: Image)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveImageInfos(images: List<Image>)

    @Update(onConflict = OnConflictStrategy.REPLACE)
    suspend fun updateImageInfo(image: Image)

    @Update(onConflict = OnConflictStrategy.REPLACE)
    suspend fun updateImageInfos(images: List<Image>)

    @Query("delete from image where image_id = :imageId")
    suspend fun deleteImageInfo(imageId: Long)

    @Query("delete from image where image_id in (:imageIds)")
    suspend fun deleteImages(imageIds: List<String>)
}