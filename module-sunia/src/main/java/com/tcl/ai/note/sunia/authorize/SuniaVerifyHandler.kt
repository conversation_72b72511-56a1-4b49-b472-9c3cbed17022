package com.tcl.ai.note.sunia.authorize

import com.sunia.authlib.bean.DeviceIdType
import com.sunia.authlib.bean.URLType
import com.sunia.authlib.bean.VerifyData
import com.sunia.authlib.managers.AuthManager
import com.sunia.penengine.sdk.engine.VerifyInfo
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.suspendCoroutineWithTimeoutOrNull
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlin.coroutines.resume

object SuniaVerifyHandler {
    private val _suniaVerifyStateFlow = MutableStateFlow<VerifyInfo?>(null)
    val suniaVerifyStateFlow = _suniaVerifyStateFlow.asStateFlow()

    init {
        GlobalContext.Companion.applicationScope.launch(Dispatchers.IO) {
            while (suniaVerifyStateFlow.value == null) {
                _suniaVerifyStateFlow.value = getVerifyInfo()
                delay(3000)
            }
        }
    }

    private suspend fun getVerifyInfo() =
        suspendCoroutineWithTimeoutOrNull(3000) { cont ->
            val context = GlobalContext.Companion.instance
            val verifyDir = context.getExternalFilesDir(VERIFY_INFO_DIR)
            if (verifyDir == null) {
                cont.resume(null)
                return@suspendCoroutineWithTimeoutOrNull
            }
            if (!verifyDir.exists()) {
                if (!verifyDir.mkdirs()) {
                    cont.resume(null)
                    return@suspendCoroutineWithTimeoutOrNull
                }
            }
            val encryptPath = verifyDir.absolutePath + "/" + LICENSE_FILE
            val verifyData = VerifyData(APP_ID, PUBLIC_KEY, encryptPath, "").apply {
                deviceIdType = DeviceIdType.ANDROID_ID
                urlType = URLType.TEST.value
            }
            AuthManager.getInstance().authorize(context, verifyData) { _, p1, _ ->
                Logger.v(TAG, "OnAuthorResultCallback: $p1 APP_ID $APP_ID")
                cont.resume(
                    VerifyInfo(encryptPath).apply {
                        appId = APP_ID
                        deviceIdType = DeviceIdType.ANDROID_ID.value
                    }
                )
            }
        }

    private const val TAG = "SuniaVerifyHandler"
    private const val VERIFY_INFO_DIR = "verifyInfos"
    private const val LICENSE_FILE = "license_auth_cert.text"
    private const val APP_ID = "20250380110525152"
    private const val PUBLIC_KEY =
        "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkhvrYGuFDPJMCXIzX9hy8RpuZXTYHhOrE" +
                "xAm2EZZawoOkgw2wytXqkgE8tp6iXEZMoAlxEoRzpraX84kHQaiLRty66Xs4Nu3uG6dmuKCCU0DXK" +
                "26OwcTMe4uFiu4GW6HNZTrsXS8Xw+W2TY7R4bTNSwsakhJYG7eg2x0HfhWuY2nGs7HrK1dGyywt" +
                "qqPhnPaLrwN3aG9Z42CPhGz+2+1c1wAQbmx3LhNiLzD4vGgBEABU5/pZlnNizG3sylZzMuqfHen7ey" +
                "Sq5NpPAOSFNtHDmNmomk/1B3hhRWWpIHLWqvwysuO8OOh+9ln6T6vRfADvtn/QowRwnNqGUg" +
                "LJK5wLwIDAQAB"
}