package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group

import android.content.Context
import android.graphics.RectF
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogEvent
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogEventManager
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogType
import com.tcl.ai.note.handwritingtext.ui.categorydialog.DialogNote
import com.tcl.ai.note.handwritingtext.ui.image.InsertImage
import com.tcl.ai.note.handwritingtext.ui.image.rememberPickImageHelper
import com.tcl.ai.note.handwritingtext.ui.image.rememberTakePhotoHelper
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScalePopup
import com.tcl.ai.note.handwritingtext.ui.richtext.state.EditableTitleNavigationBarState
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.ImportActionContent
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.ImportButton
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.LassoActionContent
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MoreActionContent
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MoreButton
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.RotateActionContent
import com.tcl.ai.note.handwritingtext.utils.ImageUtils
import com.tcl.ai.note.handwritingtext.vm.draw.LassoEditEvent
import com.tcl.ai.note.handwritingtext.vm.draw.LassoRotateEvent
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.theme.GlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.voicetotext.view.widget.RecordingBarCrossfadeAnim
import com.tcl.ai.note.voicetotext.view.widget.rememberAudioPermissionHelper
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch


@Composable
fun LassoRotatePopupArea(
    state: LassoRotateEvent,
    onStateChanged: (LassoEditEvent?) -> Unit,
    density:  Density,
) {
    with(density) {
        Logger.d("SuniaDrawInkView", "show  x=${state.x}")
        val importPopupOffset = IntOffset(
            x = state.x.toInt(),
            y = state.y.toInt()
        )

        BounceScalePopup(
            onDismissRequest = {
                onStateChanged(null)
            },
            offset = importPopupOffset
        ) { closePopup ->
            RotateActionContent(state.rotate)
        }
    }
}
