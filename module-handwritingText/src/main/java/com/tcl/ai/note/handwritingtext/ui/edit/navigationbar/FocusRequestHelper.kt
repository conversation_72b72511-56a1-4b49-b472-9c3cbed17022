package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar

import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.SoftwareKeyboardController
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 辅助类，用于处理焦点请求的重试逻辑
 */
object FocusRequestHelper {
    
    private var currentFocusJob: Job? = null
    
    /**
     * 请求焦点并显示键盘，带重试机制
     * @param scope 协程作用域
     * @param focusRequester 焦点请求器
     * @param keyboardController 键盘控制器
     * @param initialDelay 初始延迟（毫秒）
     * @param retryCount 重试次数
     * @param retryDelay 重试间隔（毫秒）
     * @param canProceed 检查是否可以继续执行的回调
     */
    fun requestFocusWithRetry(
        scope: CoroutineScope,
        focusRequester: FocusRequester,
        keyboardController: SoftwareKeyboardController?,
        initialDelay: Long = 300,
        retryCount: Int = 2,
        retryDelay: Long = 200,
        canProceed: (() -> Bo<PERSON>an)? = null
    ) {
        // 取消之前的焦点请求
        currentFocusJob?.cancel()
        
        currentFocusJob = scope.launch {
            // 初始延迟
            delay(initialDelay)
            
            // 检查是否可以继续执行
            if (canProceed?.invoke() == false) {
                return@launch
            }
            
            // 尝试请求焦点（包括重试）
            repeat(retryCount) { attempt ->
                if (attempt > 0) {
                    delay(retryDelay)
                    // 每次重试前都检查是否可以继续
                    if (canProceed?.invoke() == false) {
                        return@launch
                    }
                }
                
                try {
                    focusRequester.requestFocus()
                    keyboardController?.show()
                } catch (e: Exception) {
                    // 忽略错误，继续重试
                }
            }
        }
    }
    
    /**
     * 取消当前正在进行的焦点请求
     */
    fun cancelCurrentFocusRequest() {
        currentFocusJob?.cancel()
        currentFocusJob = null
    }
}