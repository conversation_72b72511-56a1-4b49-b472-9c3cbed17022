package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.popup

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.database.entity.contentDescription
import com.tcl.ai.note.handwritingtext.database.entity.resId
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils.PopupOffsetUtils
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScalePopup
import com.tcl.ai.note.handwritingtext.ui.swatches.RecentColorsBar
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.utils.toArgbLong
import com.tcl.ai.note.utils.toPx


@Composable
fun RichTextBgSettingPopup(
    isDarkTheme:Boolean = isSystemInDarkTheme(),

    selBgMode: BgMode,
    selBgColor: Color,
    onChange:(skin: Skin) -> Unit,
    onDismiss: () -> Unit,
) {
    val density: Density = LocalDensity.current
    val screenWidthDp = LocalConfiguration.current.screenWidthDp
    val morePopupOffset = if(isTablet){
        PopupOffsetUtils.calculateMorePopupOffset(TclTheme.dimens, density)
    }else{
        IntOffset(
            x = ((screenWidthDp.dp- TclTheme.dimens.popupWidth).toPx/2).toInt(),
            y = (TclTheme.dimens.navigationBarHeight).toPx.toInt()
        )
    }


    val bgColors = arrayListOf(
        Color(Skin.defColor),
        Color(0xffFFC59D),
        Color(0xffFFE595),
        Color(0xffC8F0E7),
        Color(0xffC8E0F8),
        Color(0xffE5DCF9),
        Color(0xffFECAEC),
    )

    BounceScalePopup(
        onDismissRequest = onDismiss,
        offset = morePopupOffset,
        alignment = isTablet.judge(
            Alignment.TopEnd,
            Alignment.TopStart
        ),
        enterTransformOrigin = TransformOrigin(1f, 0f),
        exitTransformOrigin = TransformOrigin(1f, 0f),
    ){
        Box(
            modifier = Modifier
                .width(TclTheme.dimens.popupWidth)
                .wrapContentHeight()
                .defShadow(20.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth()
                    .wrapContentHeight()
                    .background(TclTheme.colorScheme.tertiaryBackground)
                    .padding(horizontal = 24.dp)
            ) {
                Spacer(Modifier.height(24.dp))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    fontSize = 20.sp,
                    lineHeight = 24.sp,
                    fontFamily = FontFamily.Default,
                    fontWeight = FontWeight.Medium,
                    color = R.color.popup_title.colorRes(),
                    text = com.tcl.ai.note.base.R.string.more_menu_page_settings.stringRes(),
                )
                Spacer(Modifier.height(12.dp))
                Row(
                    modifier = Modifier.fillMaxWidth()
                ){
                    BgMode.entries.forEach { bgMode ->
                        BgStyleItem(bgMode,bgMode == selBgMode) {
                            onChange(Skin(
                                bgMode = bgMode,
                                color = selBgColor.toArgbLong()))
                        }
                        Spacer(Modifier.width(16.dp))
                    }
                }
                Spacer(Modifier.height(4.dp))
                Row(
                    modifier = Modifier.fillMaxWidth().height(48.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {

                    RecentColorsBar(
                        modifier = Modifier.fillMaxWidth(),
                        colors =isDarkTheme.judge(
                            bgColors.map { it.inverseColor() },
                            bgColors
                        ),

                        selectedColor = isDarkTheme.judge(
                            selBgColor.inverseColor(),
                            selBgColor
                        ),
                        onColorClick = { color ->
                            onChange(Skin(
                                bgMode = selBgMode,
                                color = isDarkTheme.judge(color.inverseColor(),color).toArgbLong()))
                        },
                        isDrawCircle = false
                    )
                }
                Spacer(Modifier.height(16.dp))
            }
        }

    }
}

@Composable
internal fun BgStyleItem(
    bgMode: BgMode,
    isCheck: Boolean,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onCheck:(bgMode: BgMode) -> Unit) {
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .clearAndSetSemantics {
                this.contentDescription = context.getString(bgMode.contentDescription())
            }
            .size(56.dp,80.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(
                color = (darkTheme && isCheck).judge(R.color.skin_card_bg.colorRes(), Color.Transparent),
                shape = RoundedCornerShape(8.dp)
            )
            .border(
                width = 2.dp,
                color = if (isCheck) colorResource(R.color.skin_style_check) else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .clipToBounds()
            .clickable {
                onCheck(bgMode)
            }

    ) {
        Image(
            painter = painterResource(bgMode.resId()),
            contentDescription = "",
            colorFilter = darkTheme.judge(
                isCheck.judge(
                    ColorFilter.tint(Color(0xff3C3C3C)),
                    ColorFilter.tint(Color(0xff424242))
                ),
                ColorFilter.tint(Color(0xffE5E5E5))
            ),
            modifier = Modifier
                .fillMaxSize()

        )

    }
}