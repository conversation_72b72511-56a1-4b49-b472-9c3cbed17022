package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tct.theme.core.designsystem.component.TclLoadingDialog

@Composable
fun ShowTextCopyLoadingPopup(
    modifier: Modifier = Modifier,
    richTextViewModel2: RichTextViewModel2 = hiltViewModel(),
) {
    val isShow by richTextViewModel2.richTextCopyLoading.collectAsState()
    TclLoadingDialog(
        show = isShow,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
        ),
        onDismissRequest = {
            richTextViewModel2.cancelCopyOrPaste()
        },
    )
}