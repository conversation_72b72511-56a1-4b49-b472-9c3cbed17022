package com.tcl.ai.note.handwritingtext.ui.swatches

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.ui.widget.CustomRadioButton
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes


@Composable
fun TabletColorPalette(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    modifier: Modifier = Modifier,
    collectColors:List<Color>,
    curPenColor: PenColor,
    onConfirm:(penColor:PenColor) ->Unit,
    onDismiss: () -> Unit,

){

    val bottomButtonHeight = 44.dp

    var selectedColor by remember (curPenColor.color) {
        mutableStateOf(curPenColor.color)
    }

    var alpha  by remember{
        mutableIntStateOf(100)
    }
    val scrollState = rememberScrollState()

    Column(
        modifier = modifier
        .wrapContentHeight()
        .verticalScroll(scrollState)
        .background(color = TclTheme.colorScheme.tertiaryBackground),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(24.dp))
        Text(
            modifier = Modifier.fillMaxWidth().padding(horizontal = 24.dp),
            fontSize = 20.sp,
            lineHeight = 24.sp,
            textAlign = TextAlign.Start,
            fontFamily = FontFamily.Default,
            fontWeight = FontWeight.Medium,
            color = com.tcl.ai.note.handwritingtext.R.color.popup_title.colorRes(),
            text = R.string.swatches_popup_tool.stringRes(),
        )
        Spacer(modifier = Modifier.height(19.dp))
        ColorSwatchGrid(
            modifier = Modifier.fillMaxWidth().wrapContentWidth(),
            selColor =selectedColor
        ) { color ->
            selectedColor = color
        }
        Spacer(modifier = Modifier.height(3.dp))
        if(collectColors.isNotEmpty()){
            Row(
                modifier = Modifier.fillMaxWidth().height(48.dp).padding(horizontal = 24.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {

                RecentColorsBar(
                    modifier = Modifier.fillMaxWidth(),
                    colors = collectColors,
                    selectedColor = selectedColor,
                    onColorClick = { color ->
                        selectedColor = color
                    }
                )
            }
        }

        /*AlphaSliderWithControls(
            modifier= Modifier
                .fillMaxWidth()
                .height(48.dp)
                .padding(start = 20.dp, end = 24.dp),
            baseColor = selectedColor,
            alphaValue =alpha,
            onAlphaChanged = { value ->
                alpha = value

            }
        )*/
        BottomButton(
            modifier = Modifier.fillMaxWidth().padding(horizontal = 20.dp),
            bottomButtonHeight = bottomButtonHeight,
        ){ isOk ->
            if(isOk){
                val penColor = PenColor(color = isDarkTheme.judge(
                    selectedColor.inverseColor(),
                    selectedColor
                ) , alpha = alpha)
                onConfirm(penColor)
            }
            onDismiss()
        }

        Spacer(modifier = Modifier.height(16.dp))

    }
}
@Composable
fun BottomButton(
    modifier: Modifier = Modifier,
    bottomButtonHeight: Dp = 44.dp,
    onClick: (isOk: Boolean) -> Unit
){
    Row(
        modifier = modifier.height(bottomButtonHeight),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .fillMaxHeight()
                .clip(RoundedCornerShape(bottomButtonHeight/2))
                .clickable {
                    onClick(false)
                }
                ,
            contentAlignment = Alignment.Center
        ){
            Text(
                text = R.string.cancel.stringRes(),
                fontSize = 16.sp,
                color = com.tcl.ai.note.handwritingtext.R.color.btn_text_blue.colorRes(),
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center)
        }
        Spacer(modifier = Modifier.width(8.dp))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .fillMaxHeight()
                .clip(RoundedCornerShape(bottomButtonHeight/2))
                .clickable {
                    onClick(true)
                },
            contentAlignment = Alignment.Center
        ){
            Text(
                text = R.string.done.stringRes(),
                fontSize = 16.sp,
                color = com.tcl.ai.note.handwritingtext.R.color.btn_text_blue.colorRes(),
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
        }

    }
}


@Composable
fun DiagonalSplitSquare(
    color: Color,
    alpha: Float,
    sizeDp: Int = 18
) {
    val cornerRadius = 3.dp
    Canvas(modifier = Modifier.size(sizeDp.dp).background(
        color = Color.Transparent,
        shape = RoundedCornerShape(cornerRadius)
    ).clip(RoundedCornerShape(cornerRadius)) ) {
        val width = size.width
        val height = size.height

        val color1Path = Path().apply {
            moveTo(0f, 0f)
            lineTo(0f, height)
            lineTo(width, 0f)
            close()
        }
        drawPath(color1Path, color = color)

        drawLine(
            color =color,
            start = Offset(width, 0f),
            end = Offset(0f, height),
            strokeWidth = 1.dp.toPx()
        )
        val color2Path = Path().apply {
            moveTo(0f, height)
            lineTo(width, height)
            lineTo(width, 0f)
            close()
        }
        drawPath(color2Path, color = color,alpha = alpha)
    }

}

/**
 * 个人收藏颜色栏
 */
@Composable
fun RecentColorsBar(
    modifier: Modifier = Modifier,
    isDrawCircle: Boolean = true,
    colors: List<Color>,
    selectedColor: Color,
    onColorClick: (Color) -> Unit
) {
    val context = LocalContext.current
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        colors.forEach { color ->
            val hsl = color.toHslArray()
            val desc = getColorDescription(context,hsl[0], hsl[1], hsl[2])
            Box(
                modifier = Modifier.size(24.dp).semantics {
                    this.contentDescription =context.getString(R.string.color_swatch_collection).plus(desc)
                    this.role = Role.Button
                },
                contentAlignment = Alignment.Center
            ){
                CustomRadioButton(
                    modifier = Modifier.invisibleSemantics(),
                    selected = color == selectedColor,
                    color = color,
                    onClick = {
                        onColorClick(color)
                    },
                    outerSize =20.dp,
                    isDrawCircle = isDrawCircle
                )
            }



        }
    }
}