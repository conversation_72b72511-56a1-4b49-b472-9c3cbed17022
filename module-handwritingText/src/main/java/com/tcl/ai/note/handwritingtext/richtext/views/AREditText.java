package com.tcl.ai.note.handwritingtext.richtext.views;

import static java.lang.Math.min;

import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.ClipDescription;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.text.Editable;
import android.text.InputType;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.AlignmentSpan;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import com.tcl.ai.note.handwritingtext.database.entity.Note;
import com.tcl.ai.note.handwritingtext.richtext.converter.RichTextStyleEntityToSpanConverter;
import com.tcl.ai.note.handwritingtext.richtext.data.StyleRange;
import com.tcl.ai.note.handwritingtext.richtext.history.RichTextEditOperation;
import com.tcl.ai.note.handwritingtext.richtext.history.RichTextUndoRedoManager;
import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener;
import com.tcl.ai.note.handwritingtext.richtext.spans.AreUnderlineSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListBulletSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Alignment;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_BackgroundColor;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Bold;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontColor;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_FontSize;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Italic;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListBullet;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_ListNumber;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Strikethrough;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Underline;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming;
import com.tcl.ai.note.handwritingtext.richtext.styles.IARE_Style;
import com.tcl.ai.note.handwritingtext.richtext.utils.InputConnectionCEWrapper;
import com.tcl.ai.note.handwritingtext.ui.richtext.RichTextController;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextKTUtilsKt;
import com.tcl.ai.note.handwritingtext.ui.richtext.base.BaseRichTextEditView;
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import android.text.InputFilter;
import android.text.Spanned;

import androidx.core.content.ContextCompat;
import com.tcl.ai.note.base.R;

/**
 * All Rights Reserved.
 * <AUTHOR> Liu
 * Android RichEditor EditText
 * 扩展自 AppCompatEditText，用于实现富文本输入与样式控制。
 * 支持粗体、斜体、下划线、对齐、列表、待办事项等多种格式。

 * 具备：
 * - 富文本样式列表管理
 * - 样式Status监听回调
 * - 对齐状态同步Compose/工具栏选中
 * - 富文本内容与html互转
 * - 监控输入变化与光标变化，自动同步按钮激活状态等
 *
 */
public class AREditText extends BaseRichTextEditView {

	// 日志打印开关
	private static boolean LOG = true;

	// 富文本内部状态监控开关
	private boolean APPLY_MONITORING = true;
	private boolean STORAGE_MONITORING = true;
	private boolean UISHOW_MONITORING = true;

	// 修正光标功能相关字段
	private boolean mShouldFixCursor = false;
	private int mSelection;

	// 样式对象集合，存储所有已注册的样式
	private final List<IARE_Style> sStylesList = new ArrayList<>();

	// 上下文对象
	private Context mContext;

	// 关联的Note对象
	private Note mNote;

	// 文本输入监听器
	private TextWatcher mTextWatcher;

	// 行距
	private float mLineSpace;

	// 保存内容到内存的任务Runnable
    private Runnable mSaveContentToMemoryTask;

	// 样式变更监听器(由RichTextViewHolder绑定)
	protected StyleStatusListener mStyleStatusListener;

	// 添加历史记录管理器
	private final RichTextUndoRedoManager mRichTextUndoRedoManager;

	// 撤销/重做状态监听器
	private UndoRedoStateListener mUndoRedoStateListener;

	// 复制监听
	private OnCopyListener mOnCopyListener;
	private boolean  mPreview;

	private boolean isDestroyed = false;

	// 是否输入回车换行符
	private boolean isEnter = false;

	// 富文本最大输入长度
	private int maxContentLength = RichTextController.MAX_CONTENT_LENGTH;

	// 保存最后删除文字前的样式状态
	private StyleState mLastDeletedStyleState = null;
	// 超限时回调（toast触发）
	private OnLimitListener onLimitListener;

	private int resetPasteStart = -1;
	private int resetPasteCount = 0;
	private  boolean isPasting = false;
	// 是否重置了粘贴，防止取消粘贴后，内容被粘贴进去
	private  boolean isResetPasteSuccess = false;

	/**
	 * 样式状态保存类
	 */
	private static class StyleState {
		boolean bold = false;
		boolean italic = false;
		boolean underline = false;
		boolean strikethrough = false;
		Integer fontColor = null;
		Integer bgColor = null;
		Integer fontSize = null;

		StyleState(boolean bold, boolean italic, boolean underline, boolean strikethrough,
				   Integer fontColor, Integer bgColor, Integer fontSize) {
			this.bold = bold;
			this.italic = italic;
			this.underline = underline;
			this.strikethrough = strikethrough;
			this.fontColor = fontColor;
			this.bgColor = bgColor;
			this.fontSize = fontSize;
		}
	}

	/**
	 * 保存当前位置的样式状态
	 * @param pos 要检查的位置
	 */
	private void saveCurrentStyleState(int pos) {
		Editable editable = getEditableText();
		if (editable == null || pos < 0 || pos >= editable.length()) {
			return;
		}

		// 获取当前位置的样式状态
		boolean bold = hasCharStyleOnPos(editable, pos, android.text.style.StyleSpan.class, android.graphics.Typeface.BOLD);
		boolean italic = hasCharStyleOnPos(editable, pos, android.text.style.StyleSpan.class, android.graphics.Typeface.ITALIC);
		boolean underline = hasCharStyleOnPos(editable, pos, AreUnderlineSpan.class, null);
		boolean strikethrough = hasCharStyleOnPos(editable, pos, StrikethroughSpan.class, null);
		Integer fontColor = getColorOnPos(editable, pos);
		Integer bgColor = getBgColorOnPos(editable, pos);
		Integer fontSize = getFontSizeOnPos(editable, pos);

		mLastDeletedStyleState = new StyleState(bold, italic, underline, strikethrough, fontColor, bgColor, fontSize);
	}

	/**
	 * 检查当前行是否为空（没有实际文字内容）
	 * @param cursorPosition 光标位置
	 * @return true 如果当前行为空或只包含零宽字符
	 */
	private boolean isCurrentLineEmpty(int cursorPosition) {
		try {
			Editable editable = getEditableText();
			if (editable == null || editable.length() == 0) {
				return true;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return true;
			}

			int line = layout.getLineForOffset(cursorPosition);
			int lineStart = layout.getLineStart(line);
			int lineEnd = layout.getLineEnd(line);

			// 如果行结束位置有换行符，不包括换行符
			if (lineEnd > lineStart && lineEnd <= editable.length() && editable.charAt(lineEnd - 1) == '\n') {
				lineEnd--;
			}

			// 如果行为空或只有换行符，认为行为空
			if (lineEnd <= lineStart) {
				return true;
			}

			// 获取该行的内容
			CharSequence lineContent = editable.subSequence(lineStart, lineEnd);

			// 检查行内容是否只包含零宽字符
			for (int i = 0; i < lineContent.length(); i++) {
				char c = lineContent.charAt(i);
				// 如果发现非零宽字符的内容，说明有实际文字
				if (c != Constants.ZERO_WIDTH_SPACE_INT) {
					return false;
				}
			}

			// 如果只包含零宽字符或为空，则认为行为空
			return true;
		} catch (Exception e) {
			Logger.e("AREditText", "Error checking if line is empty: " + e.getMessage());
			// 出错时保守处理，认为行不为空
			return false;
		}
	}

	/**
	 * 同步样式对象状态与保存的样式状态
	 */
	private void syncStyleObjectsWithSavedState() {
		if (mLastDeletedStyleState == null || sStylesList == null) {
			return;
		}

		for (IARE_Style style : sStylesList) {
			if (style instanceof ARE_Bold) {
				style.updateCheckStatus(mLastDeletedStyleState.bold);
			} else if (style instanceof ARE_Italic) {
				style.updateCheckStatus(mLastDeletedStyleState.italic);
			} else if (style instanceof ARE_Underline) {
				style.updateCheckStatus(mLastDeletedStyleState.underline);
			} else if (style instanceof ARE_Strikethrough) {
				style.updateCheckStatus(mLastDeletedStyleState.strikethrough);
			} else if (style instanceof ARE_FontColor) {
				style.updateCheckStatus(mLastDeletedStyleState.fontColor != null);
			} else if (style instanceof ARE_BackgroundColor) {
				style.updateCheckStatus(mLastDeletedStyleState.bgColor != null);
			} else if (style instanceof ARE_FontSize) {
				style.updateCheckStatus(mLastDeletedStyleState.fontSize != null);
			}
		}
	}

	/**
	 * 自动应用当前激活的字符样式到新输入的文字
	 * @param editable 可编辑文本
	 * @param start 新输入文字的开始位置
	 * @param end 新输入文字的结束位置
	 */
	private void applyActiveCharacterStylesToNewInput(Editable editable, int start, int end) {
		if (sStylesList == null || start >= end) {
			return;
		}

		// 遍历所有字符样式，将激活的样式应用到新输入的文字
		for (IARE_Style style : sStylesList) {
			// 只处理字符级别的样式（粗体、斜体、下划线、删除线、字体颜色、背景色、字号）
			if (style instanceof ARE_Bold || style instanceof ARE_Italic ||
				style instanceof ARE_Underline || style instanceof ARE_Strikethrough ||
				style instanceof ARE_FontColor || style instanceof ARE_BackgroundColor ||
				style instanceof ARE_FontSize) {

				// 如果样式处于激活状态，应用到新输入的文字
				if (style.getIsChecked()) {
					try {
						// 临时关闭监控，避免递归调用
						boolean wasMonitoring = APPLY_MONITORING;
						APPLY_MONITORING = false;

						// 应用样式到新输入的文字范围
						style.applyStyle(editable, start, end, false);

						// 恢复监控状态
						APPLY_MONITORING = wasMonitoring;
					} catch (Exception e) {
						Logger.e("AREditText", "Error applying style to new input: " + e.getMessage());
					}
				}
			}
		}
	}
	/**
	 * 设置最大长度同时应用过滤器
	 * @param max
	 */
	public void setMaxContentLength(int max) {
		this.maxContentLength = max;
		// 每次变化都要重新应用过滤器
		applyMaxLengthFilter();
	}
	// 设置内容长度超限监听器
	public void setOnLimitListener(OnLimitListener l) { this.onLimitListener = l; }

	// 超限监听接口（toast提示栏可用）
	public interface OnLimitListener {
		void onInputLimit(); // 输入被拦截
		void onPasteLimit(); // 粘贴被拦截
	}

	/**
	 * 检查是否已销毁
	 */
	public boolean isDestroyed() {
		return isDestroyed;
	}

	// 构造方法
	public AREditText(Context context) {
		this(context, null);
	}
	public AREditText(Context context, AttributeSet attrs) {
		this(context, attrs, 0);
	}
	public AREditText(Context context, AttributeSet attrs, int defStyleAttr) {
		super(context, attrs, defStyleAttr);
		mContext = context;
//		initGlobalValues();
//		init();
		// 注册输入相关监听(样式、监控等)
		setupListener();
		mLineSpace = getLineSpacingExtra();
		this.setMovementMethod(EditMovementMethod.getInstance());
		//disable framework textview suggestion for monkey test
		setInputType(getInputType() | InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS);

		// 初始化历史记录管理器
		mRichTextUndoRedoManager = new RichTextUndoRedoManager(this);

		applyMaxLengthFilter();
	}

	// 初始化屏幕尺寸全局值
	private void initGlobalValues() {
//		int[] wh = Util.getScreenWidthAndHeight(mContext);
//		Constants.SCREEN_WIDTH = wh[0];
//		Constants.SCREEN_HEIGHT = wh[1];
	}

	// 设置UI属性，默认为白底、多行编辑
	private void init() {
        // this.setMovementMethod(new AREMovementMethod());
		this.setFocusableInTouchMode(true);
		this.setBackgroundColor(Color.WHITE);
		this.setInputType(EditorInfo.TYPE_CLASS_TEXT | EditorInfo.TYPE_TEXT_FLAG_MULTI_LINE
				| EditorInfo.TYPE_TEXT_FLAG_NO_SUGGESTIONS);
		int padding = 8;
		padding = Util.getPixelByDp(mContext, padding);
		this.setPadding(padding, padding, padding, padding);
		this.setTextSize(TypedValue.COMPLEX_UNIT_SP, Constants.DEFAULT_FONT_SIZE);
	}

	/**
	 * 设置富文本样式变化Status监听器（RichTextViewHolder实现并注入，用于回报UI/状态管理/Compose）
	 * @param listener
	 */
	public void setStyleStatusListener(StyleStatusListener listener) {
		this.mStyleStatusListener = listener;
	}

	/** 注册样式（如粗体/斜体/对齐...）
	 * 设置后，输入内容时会自动生效并调用监听器回调
	 */
	public void setInStylesList(IARE_Style style) {
		style.setEditText(this);
		style.setisValid(true);
		sStylesList.add(style);
	}

	/** 清空所有样式注册 */
	public void clearStylesList(){
		for(IARE_Style style : sStylesList) {
			style.updateCheckStatus(false);
			style.setisValid(false);
			style.setEditText(null);
		}
		sStylesList.clear();
	}

	// 内容变化监听
	private OnContentChangedListener onContentChangedListener;
	public interface OnContentChangedListener {
		void onContentChanged(CharSequence text);
	}

	// 用于记录删除前的列表span信息
	private java.util.List<Object> spansToRemoveAfterDelete = new java.util.ArrayList<>();
	public void setOnContentChangedListener(OnContentChangedListener listener) {
		this.onContentChangedListener = listener;
	}

	public void setOnCopyListener(OnCopyListener listener) {
		this.mOnCopyListener = listener;
	}

	public void saveContent(String content) {
		if (onContentChangedListener != null) {
			onContentChangedListener.onContentChanged(content);
		}
	}

	/**
	 * 拦截超长输入和超长粘贴的InputFilter
	 */
	private final InputFilter maxLengthFilter = new InputFilter() {
		@Override
		public CharSequence filter(CharSequence source, int start, int end,
								   Spanned dest, int dstart, int dend) {
			// dest: 旧有内容，source: 新输内容，dstart/dend：要替换的区间
			// 剩余可插入字数
			int keep = maxContentLength - (dest.length() - (dend - dstart));
			if (keep <= 0) {
				// 已达到最大限制，拒绝输入
				if (onLimitListener != null) onLimitListener.onInputLimit();
				return ""; // 拦截
			} else if (keep >= end - start) {
				// 新内容可以完全写进
				return null;
			} else {
				// 只能截取局部，新内容部分能进来，加提示
				if (onLimitListener != null) onLimitListener.onInputLimit();
				return source.subSequence(start, start + keep);
			}
		}
	};

	/**
	 * 应用/刷新最大长度InputFilter，避免多个filter重复设置
	 */
	private void applyMaxLengthFilter() {
		InputFilter[] old = getFilters();
		if (old == null) {
			setFilters(new InputFilter[]{maxLengthFilter});
		} else {
			ArrayList<InputFilter> newf = new ArrayList<>();
			boolean has = false;
			for (InputFilter f : old) {
				if (f == maxLengthFilter) has = true;
				newf.add(f);
			}
			if (!has) newf.add(maxLengthFilter);
			setFilters(newf.toArray(new InputFilter[0]));
		}
	}

	/**
	 * 添加输入/文本的核心监听（setupListener内部调用）
	 */
	private void setupListener() {
		setupTextWatcher();
	} // #End of setupListener()

	/**
	 * 文本变化监听＋删除监听
	 */
	private void setupTextWatcher() {
		mTextWatcher = new TextWatcher() {

			int startPos = 0;
			int endPos = 0;
			int before = 0;
			int count = 0;
			boolean isDelete;
			// 是否存在自定义格式数据
			boolean isPasteCustomStyle;


			@Override
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {
				if (!APPLY_MONITORING) {
					return;
				}
				if (LOG) {
					Logger.d("beforeTextChanged:: s = " + s + "---size=" + AREditText.this.getEditableText().length() +" start = " + start + "---count = " + count
							+ ", after = " + after);
				}

				// 清空之前记录的span信息
				spansToRemoveAfterDelete.clear();

				// 如果是删除操作（count > 0 且 after == 0），保存删除前的样式状态和列表span信息
				if (count > 0 && after == 0) {
					// 总是保存被删除文字的样式状态（删除范围内最后一个字符的样式）
					// 这样可以确保保存的是当前行最后一个被删除字符的实际样式
					int savePos = Math.max(0, start + count - 1);
					if (savePos < s.length()) {
						saveCurrentStyleState(savePos);
					}

					// 分析删除范围，确定需要删除哪些列表span
					analyzeDeleteRangeAndRecordSpans(s, start, count);
				}
			}

			@Override
			public void onTextChanged(CharSequence s, int start, int before, int count) {
				if (!APPLY_MONITORING) {
					return;
				}

				if (LOG) {
					Logger.d("onTextChanged:: s = " + s + "---size=" + AREditText.this.getEditableText().length() +" start = " + start + "---count = " + count + "---before = "
							+ before);
				}
				this.startPos = start;
				this.endPos = start + count;
				this.before = before;
				this.count = count;
				// 记录文本变化操作
                isDelete = before > 0 && count == 0;
				if (before >= 0 && count > 0) {
					// 新增字符
					String inputText = s.toString().substring(start, start + count);
					isPasteCustomStyle = inputText.contains(RichTextStyleEntityToSpanConverter.TODO_FLAG)
							|| inputText.contains(RichTextStyleEntityToSpanConverter.TODO_FLAG_CHECK)
							|| inputText.contains(RichTextStyleEntityToSpanConverter.BULLET_FLAG)
							|| inputText.contains("1.");
					// 输入换行符
					if (("\n").equals(inputText)) {
						isEnter = true;
					}
				}
			}

			@Override
			public void afterTextChanged(Editable s) {
				if (!APPLY_MONITORING) {
					return;
				}

				if (LOG) {
					Logger.d("afterTextChanged:: s = " + s + "---size=" + AREditText.this.getEditableText().length() +" startPos=" + startPos + "---endPos=" + endPos);
				}

				if (endPos <= startPos) {
					Logger.d("User deletes: start == " + startPos + "---endPos == " + endPos);
				}

				// 只有输入回车换行符时处理待办、段落、数字这三个样式的刷新
				if(isEnter){
					isEnter = false;
					// 找到段落结束位置
					// 注意：这里startPos+1, 是因为当前已经输入了"\n"换行符，紧接着后面需要去掉这个换行符来计算段落
					int paragraphEnd = NewlineSpanChecker.findParagraphEnd(s, startPos+1);
					// 段落结束位置可能为-1，则取当前行结束位置
					if (paragraphEnd == -1){
						paragraphEnd = endPos;
					}
					// 回调所有样式applyStyle方法，实时处理输入
					for (IARE_Style style : sStylesList) {
						if (style.needApplyStyle()) {
							// 判断是否是有序列表，因为只有有序列表需要刷新列表编号，控制刷新范围
							if (style instanceof ARE_ListNumber) {
								((ARE_ListNumber)style).applyStyle(s, startPos, endPos, paragraphEnd, false);
							} else {
								style.applyStyle(s, startPos, endPos, false);
							}
						}
					}
				}

				// 处理字符样式的自动应用（粗体、斜体、下划线等）
				if (endPos > startPos && !isDelete && !isPasteCustomStyle && !isEnter) {
					// 如果有保存的样式状态，先同步样式对象状态
					if (mLastDeletedStyleState != null) {
						syncStyleObjectsWithSavedState();
					}

					// 自动应用当前激活的字符样式到新输入的文字
					applyActiveCharacterStylesToNewInput(s, startPos, endPos);
				}

				// 处理删除操作，特别是对列表和待办事项的处理
				if (isDelete && !spansToRemoveAfterDelete.isEmpty()) {
					// 处理在beforeTextChanged中记录的需要删除的列表span
					boolean hasNumberSpansDeleted = false;
					Object remainingListSpan = null; // 保留的列表span（开始位置的span）

					// 收集需要清理的零宽字符位置
					java.util.List<Integer> zeroWidthPositions = new java.util.ArrayList<>();

					// 检查是否是全选删除（删除起始位置为0且删除后文本为空或接近为空）
					boolean isSelectAllDelete = (startPos == 0) && (s.length() <= 1);

					// 只有在非全选删除的情况下才考虑保留开始位置的列表span
					if (!isSelectAllDelete) {
						Layout layout = getLayout();
						if (layout != null) {
							int startLine = layout.getLineForOffset(startPos);
							int lineStart = layout.getLineStart(startLine);
							int lineEnd = layout.getLineEnd(startLine);

							// 检查是否是从行首删除（可能导致内容合并到前一行）
							boolean isDeleteFromLineStart = (startPos == lineStart);

							Object targetSpan = null;

							if (isDeleteFromLineStart && startLine > 0) {
								// 从行首删除，内容会合并到前一行，应该保留前一行的span
								int prevLine = startLine - 1;
								int prevLineStart = layout.getLineStart(prevLine);
								int prevLineEnd = layout.getLineEnd(prevLine);

								// 查找前一行的列表span
								UpcomingListSpan[] prevUpcomingSpans = s.getSpans(prevLineStart, prevLineEnd, UpcomingListSpan.class);
								if (prevUpcomingSpans != null && prevUpcomingSpans.length > 0) {
									targetSpan = prevUpcomingSpans[0];
								} else {
									ListBulletSpan[] prevBulletSpans = s.getSpans(prevLineStart, prevLineEnd, ListBulletSpan.class);
									if (prevBulletSpans != null && prevBulletSpans.length > 0) {
										targetSpan = prevBulletSpans[0];
									} else {
										ListNumberSpan[] prevNumberSpans = s.getSpans(prevLineStart, prevLineEnd, ListNumberSpan.class);
										if (prevNumberSpans != null && prevNumberSpans.length > 0) {
											targetSpan = prevNumberSpans[0];
										}
									}
								}
							}

							// 如果没有找到前一行的span，或者不是从行首删除，则查找当前行的span
							if (targetSpan == null) {
								UpcomingListSpan[] upcomingSpans = s.getSpans(lineStart, lineEnd, UpcomingListSpan.class);
								if (upcomingSpans != null && upcomingSpans.length > 0) {
									targetSpan = upcomingSpans[0];
								} else {
									ListBulletSpan[] bulletSpans = s.getSpans(lineStart, lineEnd, ListBulletSpan.class);
									if (bulletSpans != null && bulletSpans.length > 0) {
										targetSpan = bulletSpans[0];
									} else {
										ListNumberSpan[] numberSpans = s.getSpans(lineStart, lineEnd, ListNumberSpan.class);
										if (numberSpans != null && numberSpans.length > 0) {
											targetSpan = numberSpans[0];
										}
									}
								}
							}

							// 只有当找到的span不在删除列表中时，才保留它
							if (targetSpan != null && !spansToRemoveAfterDelete.contains(targetSpan)) {
								remainingListSpan = targetSpan;
							}
						}
					}

					for (Object spanObj : spansToRemoveAfterDelete) {
						// 如果是开始位置的span，不删除，后续需要扩展其范围
						if (spanObj == remainingListSpan) {
							continue;
						}

						if (spanObj instanceof UpcomingListSpan) {
							UpcomingListSpan span = (UpcomingListSpan) spanObj;
							// 检查span是否还存在于文本中
							int spanStart = s.getSpanStart(span);
							int spanEnd = s.getSpanEnd(span);
							if (spanStart >= 0 && spanEnd >= 0) {
								// 删除span
								s.removeSpan(span);
								// 记录零宽字符位置
								if (spanStart < s.length() && s.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT) {
									zeroWidthPositions.add(spanStart);
								}
							}
						} else if (spanObj instanceof ListBulletSpan) {
							ListBulletSpan span = (ListBulletSpan) spanObj;
							// 检查span是否还存在于文本中
							int spanStart = s.getSpanStart(span);
							int spanEnd = s.getSpanEnd(span);
							if (spanStart >= 0 && spanEnd >= 0) {
								// 删除span
								s.removeSpan(span);
								// 记录零宽字符位置
								if (spanStart < s.length() && s.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT) {
									zeroWidthPositions.add(spanStart);
								}
							}
						} else if (spanObj instanceof ListNumberSpan) {
							ListNumberSpan span = (ListNumberSpan) spanObj;
							// 检查span是否还存在于文本中
							int spanStart = s.getSpanStart(span);
							int spanEnd = s.getSpanEnd(span);
							if (spanStart >= 0 && spanEnd >= 0) {
								// 删除span
								s.removeSpan(span);
								// 记录零宽字符位置
								if (spanStart < s.length() && s.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT) {
									zeroWidthPositions.add(spanStart);
								}
								hasNumberSpansDeleted = true;
							}
						}
					}

					// 从后往前删除零宽字符，避免位置偏移问题
					java.util.Collections.sort(zeroWidthPositions, java.util.Collections.reverseOrder());
					for (Integer pos : zeroWidthPositions) {
						if (pos < s.length() && s.charAt(pos) == Constants.ZERO_WIDTH_SPACE_INT) {
							s.delete(pos, pos + 1);
						}
					}

					// 扩展保留的列表span范围以包含合并的内容
					if (remainingListSpan != null) {
						expandListSpanToIncludeMergedContent(s, remainingListSpan, startPos);
					}

					// 强制修复所有列表span范围
					post(new Runnable() {
						@Override
						public void run() {
							fixAllListSpanRangesAfterDelete();
						}
					});

					// 清空记录的span信息
					spansToRemoveAfterDelete.clear();

					// 如果删除了有序列表项，需要重新编号剩余的列表项
					if (hasNumberSpansDeleted) {
						post(new Runnable() {
							@Override
							public void run() {
								for (IARE_Style style : sStylesList) {
									if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
										Util.log("AREditText, 1");
										((ARE_ListNumber) style).reNumberAllListItemsPublic(s, startPos, count);
										break;
									}
								}
							}
						});
					}
				} else if (isDelete) {
					// 处理没有预记录span的删除情况（保持原有逻辑）
					int deleteStart = startPos;
//					int deleteEnd = startPos + before;
					int paragraphStart = NewlineSpanChecker.findParagraphStart(s, startPos);
					// 找到段落结束的位置以换行符为记号
					int paragraphEnd = NewlineSpanChecker.findParagraphEnd(s, startPos);
					// 检查删除的内容前面是否有span
					boolean isSpanByHead = NewlineSpanChecker.isLineStartWithPreviousSpan(s, startPos);
					// 检查删除的内容后面是否有span
				  	boolean hasImmediateSpanAfter = NewlineSpanChecker.hasImmediateSpanAfter(s, startPos);

					// 同时满足以下两个条件，则不需要重新排序
					// 1. 如果删除的只是文本内容不涉及span,且删除内容后不是处于文本第一个索引位置
					// 2. 如果删除文本的后面紧跟着的不是SPAN，而是换行符，则不需要重新排序
					if (!isSpanByHead && !hasImmediateSpanAfter){
						return;
					}

					// 处理待办事项删除
					UpcomingListSpan[] upcomingSpans = s.getSpans(deleteStart, deleteStart, UpcomingListSpan.class);
					if (upcomingSpans != null && upcomingSpans.length > 0) {
						for (IARE_Style style : sStylesList) {
							if (style instanceof ARE_Upcoming && style.needApplyStyle()) {
								style.applyStyle(s, deleteStart, deleteStart, false);
								break;
							}
						}
					}

					// 处理无序列表删除
					ListBulletSpan[] bulletSpans = s.getSpans(deleteStart, deleteStart, ListBulletSpan.class);
					if (bulletSpans != null && bulletSpans.length > 0) {
						for (IARE_Style style : sStylesList) {
							if (style instanceof ARE_ListBullet && style.needApplyStyle()) {
								style.applyStyle(s, deleteStart, deleteStart, false);
								break;
							}
						}
					}

					// 处理有序列表删除
					ListNumberSpan[] numberSpans = s.getSpans(deleteStart, paragraphEnd, ListNumberSpan.class);
					if (numberSpans != null && numberSpans.length > 0) {
						for (IARE_Style style : sStylesList) {
							if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
								((ARE_ListNumber) style).applyStyle(s, deleteStart, deleteStart, paragraphEnd, false);
								break;
							}
						}
					} else {
						ListNumberSpan[] allListSpans;
						final int startUpdatePos;
						final int endUpdatePos;
						if (startPos == 0) { // 如果是文档开头位置
							// 获取当前段落中的有序列表项
							allListSpans = s.getSpans(startPos, paragraphEnd, ListNumberSpan.class);
							startUpdatePos = startPos;
							endUpdatePos = paragraphEnd;
						} else if (startPos == s.length() - 1) {  // 如果是文档末尾位置
							allListSpans = s.getSpans(paragraphStart, paragraphEnd, ListNumberSpan.class);
							startUpdatePos = paragraphStart;
							endUpdatePos = paragraphEnd;
						} else {  // 如果是文档中间位置
							allListSpans = s.getSpans(startPos, count, ListNumberSpan.class);
							startUpdatePos = startPos;
							endUpdatePos = count;
						}
						// 如果删除位置没有列表项，但文档中还有其他列表项，则重新编号
						if (allListSpans != null && allListSpans.length > 0) {
							// 延迟执行重新编号，避免与其他删除处理冲突
							post(() -> {
                                for (IARE_Style style : sStylesList) {
                                    if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
										Util.log("AREditText, 2");
                                        ((ARE_ListNumber) style).reNumberAllListItemsPublic(s, startUpdatePos, endUpdatePos);
                                        break;
                                    }
                                }
                            });
						}
					}
				}
				// 存在自定义格式数据，不记录撤销/重做操作， 异步处理完自定格式数据后再记录撤销/重做操作
				if (!isPasteCustomStyle) {
					RichTextKTUtilsKt.recordDeleteStyleToUndoRedo(startPos, before, count, isDelete, mRichTextUndoRedoManager);

					if (onContentChangedListener != null) {
						onContentChangedListener.onContentChanged(s);
					}
				}
				isPasting = false;
				// 处理粘贴的自定义格式数据
				if (isPasteCustomStyle) {
					isResetPasteSuccess = false;
					isPasting = true;
					resetPasteStart = startPos;
					resetPasteCount = count;
					stopApplyMonitor();
					Logger.d("User inserts: start == " + startPos + "---endPos == " + endPos);
					isPasteCustomStyle = false;
					mOnCopyListener.startPaste(s, startPos, endPos, before, count, isDelete);
				}
			}
		};

		this.addTextChangedListener(mTextWatcher);

		// 监听物理键盘删除事件，适配部分边界场景
		this.setOnKeyListener(new OnKeyListener() {
			@Override
			public boolean onKey(View v, int keyCode, KeyEvent event) {
				if (keyCode == KeyEvent.KEYCODE_DEL && event.getAction() == KeyEvent.ACTION_DOWN) {
					int selctionStart = AREditText.this.getSelectionStart();
					Layout layout = AREditText.this.getLayout();
					int line = layout.getLineForOffset(selctionStart);
					int lineStart = layout.getLineStart(line);

					// 删除整行的富文本样式 - 修改条件：当光标在行首或行内只有列表前缀字符时，且该行有列表项span时就可以删除前缀符号
					boolean canDeletePrefix = false;
					if (hasListSpanOnCurrentLine(selctionStart)) {
						// 检查是否在行首
						if (selctionStart == lineStart || selctionStart == 0) {
							canDeletePrefix = true;
						} else {
							// 检查光标前是否只有零宽字符（列表前缀）
							canDeletePrefix = isOnlyZeroWidthCharsBefore(selctionStart, lineStart);
						}
					}

					if (canDeletePrefix) {
						if (selctionStart == 0) {
							// 解决删除第一个位置的待办后，重新添加待办失效问题
							RichTextEventManager.INSTANCE.clearStyleEvent();
						}

						// 检查是否会有内容合并到前一行
						boolean willMergeContent = checkIfContentWillMerge(selctionStart);
						Object targetListSpan = null;
						if (willMergeContent) {
							targetListSpan = findTargetListSpanForMerge(selctionStart);
						}


						for (IARE_Style style : sStylesList) {
							if (style.needApplyStyle()) {
								style.removeStyle(AREditText.this.getEditableText(), selctionStart, selctionStart);
							}
						}

						// 扩展目标span范围或执行通用修复
						if (willMergeContent && targetListSpan != null) {
							final Object finalTargetSpan = targetListSpan;
							post(new Runnable() {
								@Override
								public void run() {
									expandListSpanAfterMerge(AREditText.this.getEditableText(), finalTargetSpan);
								}
							});
						} else {
							post(new Runnable() {
								@Override
								public void run() {
									fixAllListSpanRangesAfterDelete();
								}
							});
						}

						// 延迟再次修复
						postDelayed(new Runnable() {
							@Override
							public void run() {
								fixAllListSpanRangesAfterDelete();
							}
						}, 100);

						// 检查是否需要重新编号有序列表
						post(new Runnable() {
							@Override
							public void run() {
								Editable s = AREditText.this.getEditableText();
//								ListNumberSpan[] listSpans = editable.getSpans(0, editable.length(), ListNumberSpan.class);
//								if (listSpans != null && listSpans.length > 0) {
//									for (IARE_Style style : sStylesList) {
//										if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
//											Util.log("AREditText, 3");
//											((ARE_ListNumber) style).reNumberAllListItemsPublic(editable, 0, editable.length());
//											break;
//										}
//									}
//								}
								ListNumberSpan[] allListSpans;
								final int startUpdatePos;
								final int endUpdatePos;
								final int startPos = AREditText.this.getSelectionStart();
								final int count = AREditText.this.getSelectionEnd();
								int paragraphStart = NewlineSpanChecker.findParagraphStart(s, startPos);
								// 找到段落结束的位置以换行符为记号
								int paragraphEnd = NewlineSpanChecker.findParagraphEnd(s, startPos);
								if (startPos == 0) { // 如果是段落开头位置（包括文档开头位置）
									// 获取当前段落中的有序列表项
									allListSpans = s.getSpans(startPos, paragraphEnd, ListNumberSpan.class);
									startUpdatePos = startPos;
									endUpdatePos = paragraphEnd;
								} else if (startPos == s.length() - 1) {  // 如果是段落末尾位置（包含文档的结尾）
									allListSpans = s.getSpans(paragraphStart, paragraphEnd, ListNumberSpan.class);
									startUpdatePos = paragraphStart;
									endUpdatePos = paragraphEnd;
								} else {  // 如果是段落中间位置
									allListSpans = s.getSpans(startPos, paragraphEnd, ListNumberSpan.class);
									startUpdatePos = startPos;
									endUpdatePos = paragraphEnd;
								}
								// 如果删除位置没有列表项，但文档中还有其他列表项，则重新编号
								if (allListSpans != null && allListSpans.length > 0) {
									for (IARE_Style style : sStylesList) {
										if (style instanceof ARE_ListNumber && style.needApplyStyle()) {
											Util.log("AREditText, 2");
											((ARE_ListNumber) style).reNumberAllListItemsPublic(s, startUpdatePos, endUpdatePos);
											break;
										}
									}
								}
							}
						});

						// 删除前缀后，手动触发样式状态更新，确保工具栏状态同步
						post(new Runnable() {
							@Override
							public void run() {
								// 手动触发onSelectionChanged来更新样式状态
								int currentSelection = AREditText.this.getSelectionStart();
								AREditText.this.onSelectionChanged(currentSelection, currentSelection);
							}
						});
					} else {
						// 处理删除换行符的情况
						Editable editable = AREditText.this.getEditableText();
						if (editable != null && selctionStart > 0 && selctionStart < editable.length() &&
								editable.charAt(selctionStart) == '\n') {

							Object targetListSpan = findTargetListSpanForMerge(selctionStart);
							if (targetListSpan != null) {
								final Object finalTargetSpan = targetListSpan;
								postDelayed(new Runnable() {
									@Override
									public void run() {
										expandListSpanAfterMerge(AREditText.this.getEditableText(), finalTargetSpan);
									}
								}, 50);
							}
						}

						// 通用修复作为保险
						postDelayed(new Runnable() {
							@Override
							public void run() {
								fixAllListSpanRangesAfterDelete();
							}
						}, 100);
					}
				}
				return false;
			}
		});
	}


	/**
	 * 检查当前行是否有列表项span（待办、有序列表、无序列表）
	 * @param cursorPosition 当前光标位置
	 * @return true 如果该行有列表项span，false 如果没有
	 */
	private boolean hasListSpanOnCurrentLine(int cursorPosition) {
		try {
			Editable editable = getEditableText();
			if (editable == null || editable.length() == 0) {
				return false;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return false;
			}

			int line = layout.getLineForOffset(cursorPosition);
			int lineStart = layout.getLineStart(line);
			int lineEnd = layout.getLineEnd(line);

			// 检查该行是否有列表项span（待办、有序列表、无序列表）
			UpcomingListSpan[] upcomingSpans = editable.getSpans(lineStart, lineEnd, UpcomingListSpan.class);
			ListNumberSpan[] numberSpans = editable.getSpans(lineStart, lineEnd, ListNumberSpan.class);
			ListBulletSpan[] bulletSpans = editable.getSpans(lineStart, lineEnd, ListBulletSpan.class);

			return (upcomingSpans != null && upcomingSpans.length > 0) ||
				   (numberSpans != null && numberSpans.length > 0) ||
				   (bulletSpans != null && bulletSpans.length > 0);
		} catch (Exception e) {
			Logger.e("AREditText", "Error checking list span on current line: " + e.getMessage());
			// 出错时保守处理，不删除前缀符号
			return false;
		}
	}

	/**
	 * 检查光标前是否只有零宽字符（列表前缀）
	 * @param cursorPosition 当前光标位置
	 * @param lineStart 行开始位置
	 * @return true 如果光标前只有零宽字符，false 如果有其他字符
	 */
	private boolean isOnlyZeroWidthCharsBefore(int cursorPosition, int lineStart) {
		try {
			Editable editable = getEditableText();
			if (editable == null || cursorPosition <= lineStart) {
				return false;
			}

			// 检查从行开始到光标位置之间是否只有零宽字符
			for (int i = lineStart; i < cursorPosition; i++) {
				if (i < editable.length() && editable.charAt(i) != Constants.ZERO_WIDTH_SPACE_INT) {
					return false; // 发现非零宽字符
				}
			}
			return true; // 只有零宽字符
		} catch (Exception e) {
			Logger.e("AREditText", "Error checking zero width chars: " + e.getMessage());
			return false;
		}
	}

	public void resetPaste() {
		if (isPasting) {
			try {
				stopApplyMonitor();
				if (resetPasteStart != -1 && resetPasteCount > 0) {
					getEditableText().delete(resetPasteStart, resetPasteStart + resetPasteCount);
				}
				isResetPasteSuccess = true;
			} catch (Exception e) {
				Logger.e("AREditText", "Error reset paste: " + e.getMessage());
			} finally {
				resetPasteStart = -1;
				resetPasteCount = 0;
				isPasting = false;
				startApplyMonitor();
			}
		}
	}

	public void handlePaste(Editable editable, int start, int end, int before, int count, boolean isDelete) {
		try {
			Layout layout = this.getLayout();
			SpannableStringBuilder builder = new SpannableStringBuilder(editable);
			String text = builder.toString();
			List<StyleRange> styleRanges = new ArrayList<>();
			// 查找待办样式
			findCustomTag(text, styleRanges, RichTextStyleEntityToSpanConverter.TODO_FLAG, layout, "todo", false);
			findCustomTag(text, styleRanges, RichTextStyleEntityToSpanConverter.TODO_FLAG_CHECK, layout, "todo", true);
			// 查找段落样式
			findCustomTag(text, styleRanges, RichTextStyleEntityToSpanConverter.BULLET_FLAG, layout, "bullet", false);
			// 查找数字样式
			findCustomNumberTag(text, styleRanges, layout);
			RichTextStyleEntityToSpanConverter.INSTANCE.applyPasteToRichText(styleRanges, builder, layout);

			// 替换自定义标识, 注意只替换行首的标识
			int replaceCount = 0;
			replaceCount += replace(builder, RichTextStyleEntityToSpanConverter.TODO_FLAG);
			replaceCount += replace(builder, RichTextStyleEntityToSpanConverter.TODO_FLAG_CHECK);
			replaceCount += replace(builder, RichTextStyleEntityToSpanConverter.BULLET_FLAG);
			replaceCount += replaceNumber(builder);
			int finalReplaceCount = replaceCount;
			Logger.d("RichTextViewModel2", "paste 11");
			if (!isResetPasteSuccess) {
				post(new Runnable() {
					@Override
					public void run() {
						Logger.d("RichTextViewModel2", "paste 22");
						setText(builder);
						setSelection(min(end - finalReplaceCount, builder.length()));

						RichTextKTUtilsKt.recordDeleteStyleToUndoRedo(start, before, count - finalReplaceCount, isDelete, mRichTextUndoRedoManager);
						startApplyMonitor();


						if (onContentChangedListener != null) {
							onContentChangedListener.onContentChanged(builder);
						}
					}
				});
			}
		} catch (Exception e) {
			Logger.e("AREditText", "Error handle paste: " + e.getMessage());
		} finally {
			isPasting = false;
			startApplyMonitor();
		}
	}

	private void findCustomTag(String text, List < StyleRange> styleRanges, String tag, Layout layout, String styleValue, boolean isCheck) {
		int index = -1;
		while((index = text.indexOf(tag, index +1)) != -1){
			int lineNumber = layout.getLineForOffset(index);
			int lineEnd = layout.getLineEnd(lineNumber);
			int lineStart = layout.getLineStart(lineNumber);
			// 空白出粘贴
			if (lineStart == index) {
				if (lineStart != -1 && lineEnd != -1) {
					styleRanges.add(new StyleRange(lineStart, lineEnd - 1, styleValue, isCheck, 0));
				}
			} else {
				// 文本中间粘贴
				int start = getStart(text, lineStart, index);
				int end = getEnd(text, lineEnd, index);
				// 判断自定义标签是不是在行首
				if (lineStart != -1 && lineEnd != -1) {
					styleRanges.add(new StyleRange(start, end - 1, styleValue, isCheck, 0));
				}
			}
		}
	}

	private int getStart(String text, int fromIndex, int tagIndex) {
		int firstEnterIndex = text.indexOf("\n");
		if (firstEnterIndex > fromIndex) {
			return 0;
		} else {
			int index = firstEnterIndex;
			while((index = text.indexOf("\n", index +1)) != -1) {
				if (index == fromIndex - 1) {
					return fromIndex;
				}
				if (index < fromIndex - 1) {
					firstEnterIndex = index;
				} else {
					return firstEnterIndex - 1;
				}
			}
			return fromIndex;
		}
	}

	private int getEnd(String text, int fromIndex, int tagIndex) {
		int firstEnterIndex = text.indexOf("\n", tagIndex);
		if (firstEnterIndex >= fromIndex - 1) {
			return fromIndex;
		} else {
			int index = fromIndex;
			while((index = text.indexOf("\n", index +1)) != -1) {
				if (index >= fromIndex) {
					return index;
				}
			}
			return fromIndex;
		}
	}

	private void findCustomNumberTag(String text, List < StyleRange> styleRanges, Layout layout) {
		int index = -1;
		int number = 1;
		while((index = text.indexOf(number + ".", index +1)) != -1){
			int lineNumber = layout.getLineForOffset(index);
			int lineEnd = layout.getLineEnd(lineNumber);
			int lineStart = layout.getLineStart(lineNumber);

			// 空白出粘贴
			if (lineStart == index) {
				if (lineStart != -1 && lineEnd != -1) {
					styleRanges.add(new StyleRange(lineStart, lineEnd - 1, "number", false, number));
				}
			} else {
				// 文本中间粘贴
				int start = getStart(text, lineStart, index);
				int end = getEnd(text, lineEnd, index);
				// 判断自定义标签是不是在行首
				if (lineStart != -1 && lineEnd != -1) {
					styleRanges.add(new StyleRange(start, end - 1, "number", false, number));
				}
			}

			number ++;
		}
	}

	private int replace(Editable editable, String tag) {
		int replaceCount = 0;
		String text = editable.toString();
		Layout layout = this.getLayout();
		int index = -1;
		while((index = text.indexOf(tag, index +1)) != -1){
			if (isCustomStyle(index, layout)) {
				editable.delete(index, index + tag.length());
				text = editable.toString();
				replaceCount += tag.length();
			}
		}
		return replaceCount;
	}

	private int replaceNumber(Editable editable) {
		int replaceCount = 0;
		String text = editable.toString();
		Layout layout = this.getLayout();
		int index = -1;
		int number = 1;
		while((index = text.indexOf(number + ".", index +1)) != -1){
			if (isCustomStyle(index, layout)) {
				editable.delete(index, index + (number + ".").length());
				replaceCount += (number + ".").length();
			}
			text = editable.toString();
			number ++;
		}
		return replaceCount;
	}

	private boolean isCustomStyle(int index, Layout layout) {
		int lineNumber = layout.getLineForOffset(index);
		int lineEnd = layout.getLineEnd(lineNumber);
		int lineStart = layout.getLineStart(lineNumber);
		return lineStart != -1 && lineEnd != -1;
	}

	// 可选的额外光标变动监听
	public interface OnSelectionChangedListener {
		void onSelectionChanged(int selStart, int selEnd);
	}

	private OnSelectionChangedListener selectionChangedListener;

	public void setOnSelectionChangedListener(OnSelectionChangedListener listener) {
		this.selectionChangedListener = listener;
	}


	/**
	 * 重写系统onSelectionChanged：
	 * - 会在用户点击、光标移动、选区改变时被调用
	 * - 这里负责同步所有样式按钮高亮状态，并实时同步当前行的对齐状态
	 * - 调用各样式Style的updateCheckStatus
	 * - 通过StyleStatusListener回报精确的状态（粗体、列表、对齐等）
	 */
	@Override
	public void onSelectionChanged(int selStart, int selEnd) {
		if (isDestroyed) return;
		super.onSelectionChanged(selStart, selEnd);
		// 选择了文本，弹出键盘
		if (selStart != selEnd) {
			requestFocusAndShowKeyboard();
		}

		if (selectionChangedListener != null) {
			selectionChangedListener.onSelectionChanged(selStart, selEnd);
		}

		// 修正列表行的光标位置（只在没有选区时执行）
		if (selStart == selEnd) {
			// 延迟执行光标修正，确保所有文本变化都已完成
			post(new Runnable() {
				@Override
				public void run() {
					Logger.d("AREditText", "onSelectionChanged: calling fixListLineCursorPosition, cursor at " + selStart);
					fixListLineCursorPosition();
				}
			});
		}

		if (!UISHOW_MONITORING) return;

		Editable editable = this.getEditableText();

		// --------------------------
		// 选区范围定义
		// --------------------------
		int selectionStart = Math.min(selStart, selEnd);
		int selectionEnd = Math.max(selStart, selEnd);
		boolean hasSelection = selectionStart != selectionEnd;

		// 段落类样式初始值[以行为单位]
		boolean allUpcoming = false, allListNumber = false, allListBullet = false;
		// 字符类样式初始值
		boolean allBold = false, allItalic = false, allUnderline = false, allStrike = false;

		// 字体颜色/背景/大小初始值
		Integer fontColor = null;
		Integer bgColor = null;
		Integer fontSize = null;

		if (hasSelection) {
			// ====================================================================
			// [选区模式] —— 工具栏仅在选区范围“所有内容均带有该样式”时高亮（样式交集机制）
			// ====================================================================

			// 段落类：选区每一行都要被对应段落span(如待办、有序、无序列表)完整覆盖
			allUpcoming = spansCoverSelectionByLine(editable, selectionStart, selectionEnd, UpcomingListSpan.class);
			allListNumber = spansCoverSelectionByLine(editable, selectionStart, selectionEnd, ListNumberSpan.class);
			allListBullet = spansCoverSelectionByLine(editable, selectionStart, selectionEnd, ListBulletSpan.class);

			// 字符类：选区每个字符都需要带该样式span
			allBold = isAllCharStyles(editable, selectionStart, selectionEnd, android.text.style.StyleSpan.class, android.graphics.Typeface.BOLD);
			allItalic = isAllCharStyles(editable, selectionStart, selectionEnd, android.text.style.StyleSpan.class, android.graphics.Typeface.ITALIC);
			allUnderline = isAllCharStyles(editable, selectionStart, selectionEnd, AreUnderlineSpan.class, null);
			allStrike = isAllCharStyles(editable, selectionStart, selectionEnd, StrikethroughSpan.class, null);

			// 字体属性：只有选区所有字符都相同时才返回属性，否则为null
			fontColor = getCommonForegroundColor(editable, selectionStart, selectionEnd);
			bgColor = getCommonBackgroundColor(editable, selectionStart, selectionEnd);
			fontSize = getCommonFontSize(editable, selectionStart, selectionEnd);
		} else {
			// ====================================================================
			// [光标模式，无选区] —— 判断光标所在位置是否被样式覆盖
			// ====================================================================

			// 检查当前行是否为空
			boolean isLineEmpty = isCurrentLineEmpty(selectionStart);

			if (isLineEmpty && mLastDeletedStyleState != null) {
				// 当前行为空且有保存的样式状态
				// 如果用户在当前行主动设置了样式，使用更新后的保存状态
				// 否则使用删除前保存的样式状态
				allBold = mLastDeletedStyleState.bold;
				allItalic = mLastDeletedStyleState.italic;
				allUnderline = mLastDeletedStyleState.underline;
				allStrike = mLastDeletedStyleState.strikethrough;
				fontColor = mLastDeletedStyleState.fontColor;
				bgColor = mLastDeletedStyleState.bgColor;
				fontSize = mLastDeletedStyleState.fontSize;

				// 段落类样式仍然按照原有逻辑判断
				int textLen = editable.length();
				int cursor = Math.max(0, Math.min(selectionStart, textLen - 1));
				int checkPos = cursor > 0 ? cursor - 1 : cursor;
				allUpcoming = hasParagraphSpanOnPos(editable, checkPos, UpcomingListSpan.class);
				allListNumber = hasParagraphSpanOnPos(editable, checkPos, ListNumberSpan.class);
				allListBullet = hasParagraphSpanOnPos(editable, checkPos, ListBulletSpan.class);
			} else {
				// 按照原有逻辑判断样式状态
				// 取光标左侧字符位置 (避免在段首时越界，始终合法)
				int textLen = editable.length();
				int cursor = Math.max(0, Math.min(selectionStart, textLen - 1));
				int checkPos = cursor > 0 ? cursor - 1 : cursor;

				// 段落类：只要当前位置有span覆盖即可高亮
				allUpcoming = hasParagraphSpanOnPos(editable, checkPos, UpcomingListSpan.class);
				allListNumber = hasParagraphSpanOnPos(editable, checkPos, ListNumberSpan.class);
				allListBullet = hasParagraphSpanOnPos(editable, checkPos, ListBulletSpan.class);

				// 字符类：判断该字符是否有span覆盖
				allBold = hasCharStyleOnPos(editable, checkPos, android.text.style.StyleSpan.class, android.graphics.Typeface.BOLD);
				allItalic = hasCharStyleOnPos(editable, checkPos, android.text.style.StyleSpan.class, android.graphics.Typeface.ITALIC);
				allUnderline = hasCharStyleOnPos(editable, checkPos, AreUnderlineSpan.class, null);
				allStrike = hasCharStyleOnPos(editable, checkPos, StrikethroughSpan.class, null);

				// 字体属性：取光标左侧字符的span，没找到为null
				fontColor = getColorOnPos(editable, checkPos);
				bgColor = getBgColorOnPos(editable, checkPos);
				fontSize = getFontSizeOnPos(editable, checkPos);

				// 如果当前行不为空，清除保存的样式状态
				if (!isLineEmpty) {
					mLastDeletedStyleState = null;
				}
			}
		}

		// =======================================================================
		// 通知各样式控制对象与UI刷新监听
		//（每个样式对象updateCheckStatus可用于按钮高亮, 同时回调控件监听StyleStatusListener以供工具栏同步）
		// =======================================================================
		if (sStylesList != null) {
			for (IARE_Style style : sStylesList) {
				// -------- 段落类 ---------
				if (style instanceof ARE_Upcoming) {
					style.updateCheckStatus(allUpcoming);
					if (mStyleStatusListener != null) mStyleStatusListener.onTodoToggled(allUpcoming);
				} else if (style instanceof ARE_ListNumber) {
					style.updateCheckStatus(allListNumber);
					if (mStyleStatusListener != null) mStyleStatusListener.onNumberedListToggled(allListNumber);
				} else if (style instanceof ARE_ListBullet) {
					style.updateCheckStatus(allListBullet);
					if (mStyleStatusListener != null) mStyleStatusListener.onBulletedListToggled(allListBullet);
					// -------- 字符类 ---------
				} else if (style instanceof ARE_Bold) {
					style.updateCheckStatus(allBold);
					if (mStyleStatusListener != null) mStyleStatusListener.onBoldToggled(allBold);
				} else if (style instanceof ARE_Italic) {
					style.updateCheckStatus(allItalic);
					if (mStyleStatusListener != null) mStyleStatusListener.onItalicToggled(allItalic);
				} else if (style instanceof ARE_Underline) {
					style.updateCheckStatus(allUnderline);
					if (mStyleStatusListener != null) mStyleStatusListener.onUnderlineToggled(allUnderline);
				} else if (style instanceof ARE_Strikethrough) {
					style.updateCheckStatus(allStrike);
					if (mStyleStatusListener != null) mStyleStatusListener.onStrikethroughToggled(allStrike);
					// -------- 字体颜色、背景、字号 ---------
				} else if(style instanceof ARE_FontColor){
					boolean hasColor = fontColor != null; style.updateCheckStatus(hasColor);
					if(mStyleStatusListener != null) mStyleStatusListener.onFontColorApplied(hasColor ? fontColor : Color.BLACK);
				} else if(style instanceof ARE_BackgroundColor){
					boolean hasBg = bgColor != null; style.updateCheckStatus(hasBg);
					if(mStyleStatusListener != null) mStyleStatusListener.onBackgroundColorApplied(hasBg ? bgColor : Color.TRANSPARENT);
				} else if(style instanceof ARE_FontSize){
					boolean hasSize = fontSize != null; style.updateCheckStatus(hasSize);
					if(mStyleStatusListener != null) mStyleStatusListener.onFontSizeApplied(hasSize ? fontSize : 16);
				}
			}
		}

		// =======================================================================
		// 其它段落属性判定如对齐方式，仍采用“光标所在行对齐span”取最后一个生效
		// =======================================================================
		if (mStyleStatusListener != null) {
			Layout.Alignment currentAlignment = Layout.Alignment.ALIGN_NORMAL;
			int selectionLine = Util.getCurrentCursorLine(this);
			int start = Util.getThisLineStart(this, selectionLine);
			int end = Util.getThisLineEnd(this, selectionLine);
			AlignmentSpan.Standard[] alignmentSpans = editable.getSpans(start, end, AlignmentSpan.Standard.class);
			if (alignmentSpans != null && alignmentSpans.length > 0) {
				currentAlignment = alignmentSpans[alignmentSpans.length - 1].getAlignment();
			}
			mStyleStatusListener.onAlignmentApplied(currentAlignment);
		}
		ARE_Alignment.updateAllAlignmentCheckStatus(this);
	}

	/**
	 * [选区模式] 判断选区的每一行是否都被该spanClass类型的span全部覆盖（用于段落高亮交集）
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @param spanClass
	 * @return
	 * @param <T>
	 */
	private <T> boolean spansCoverSelectionByLine(Editable editable, int selStart, int selEnd, Class<T> spanClass) {
		if (selStart >= selEnd) return false;
		int pos = selStart;
		while (pos < selEnd) {
			int lineEnd = TextUtils.indexOf(editable, '\n', pos);
			if (lineEnd == -1 || lineEnd > selEnd) lineEnd = selEnd;
			boolean found = false;
			T[] spans = editable.getSpans(pos, lineEnd, spanClass);
			for (T span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (spanStart <= pos && spanEnd >= lineEnd) {
					found = true;
					break;
				}
			}
			if (!found) return false;
			pos = lineEnd + 1;
		}
		return true;
	}

	/**
	 * [光标模式] 判断某一字符是否被目标段落span覆盖（光标所在即高亮）
	 * @param editable
	 * @param pos
	 * @param spanClass
	 * @return
	 * @param <T>
	 */
	private <T> boolean hasParagraphSpanOnPos(Editable editable, int pos, Class<T> spanClass) {
		T[] spans = editable.getSpans(pos, pos + 1, spanClass);
		for (T span : spans) {
			int spanStart = editable.getSpanStart(span);
			int spanEnd = editable.getSpanEnd(span);
			if (pos >= spanStart && pos < spanEnd) return true;
		}

		// 对于段落样式（如待办事项），还需要检查光标是否在span开始位置
		// 这种情况下光标应该被认为是在span内
		int selectionStart = getSelectionStart();
		spans = editable.getSpans(selectionStart, selectionStart, spanClass);
		for (T span : spans) {
			int spanStart = editable.getSpanStart(span);
			int spanEnd = editable.getSpanEnd(span);
			if (selectionStart == spanStart && spanStart < spanEnd) return true;
		}

		return false;
	}

	/**
	 * [选区模式] 判断选区每个字符是否都被目标字符样式span覆盖
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @param spanClass
	 * @param requireStyle
	 * @return
	 */
	private boolean isAllCharStyles(Editable editable, int selStart, int selEnd, Class<?> spanClass, Integer requireStyle) {
		if (selStart == selEnd) return false;
		for (int i = selStart; i < selEnd; i++) {
			if (!hasCharStyleOnPos(editable, i, spanClass, requireStyle)) return false;
		}
		return true;
	}

	/**
	 * [光标模式] 判断指定字符是否被字符样式span覆盖
	 * @param editable
	 * @param pos
	 * @param spanClass
	 * @param requireStyle
	 * @return
	 */
	private boolean hasCharStyleOnPos(Editable editable, int pos, Class<?> spanClass, Integer requireStyle) {
		Object[] spans = editable.getSpans(pos, pos + 1, spanClass);
		for (Object span : spans) {
			if (spanClass == android.text.style.StyleSpan.class && requireStyle != null) {
				int style = ((android.text.style.StyleSpan) span).getStyle();
				if (style == requireStyle || (requireStyle == android.graphics.Typeface.BOLD && style == android.graphics.Typeface.BOLD_ITALIC) || (requireStyle == android.graphics.Typeface.ITALIC && style == android.graphics.Typeface.BOLD_ITALIC)) {
					int spanStart = editable.getSpanStart(span);
					int spanEnd = editable.getSpanEnd(span);
					if (pos >= spanStart && pos < spanEnd)
						return true;
				}
			} else {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (pos >= spanStart && pos < spanEnd)
					return true;
			}
		}
		return false;
	}

	/**
	 * [选区模式] 获取选区交集的共同字体颜色，没有则返回null
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @return
	 */
	private Integer getCommonForegroundColor(Editable editable, int selStart, int selEnd) {
		Integer color = null;
		if (selStart == selEnd) return null;
		for (int i = selStart; i < selEnd; i++) {
			ForegroundColorSpan[] spans = editable.getSpans(i, i + 1, ForegroundColorSpan.class);
			Integer thisColor = null;
			for (ForegroundColorSpan span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (i >= spanStart && i < spanEnd) {
					thisColor = span.getForegroundColor();
					break;
				}
			}
			if (thisColor == null) return null;
			if (color == null) color = thisColor;
			else if (!color.equals(thisColor)) return null;
		}
		return color;
	}

	/**
	 * 获取选区共同背景色
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @return
	 */
	private Integer getCommonBackgroundColor(Editable editable, int selStart, int selEnd) {
		Integer color = null;
		if (selStart == selEnd) return null;
		for (int i = selStart; i < selEnd; i++) {
			BackgroundColorSpan[] spans = editable.getSpans(i, i + 1, BackgroundColorSpan.class);
			Integer thisColor = null;
			for (BackgroundColorSpan span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (i >= spanStart && i < spanEnd) {
					thisColor = span.getBackgroundColor();
					break;
				}
			}
			if (thisColor == null) return null;
			if (color == null) color = thisColor;
			else if (!color.equals(thisColor)) return null;
		}
		return color;
	}

	/**
	 * 获取选区共同字体大小
	 * @param editable
	 * @param selStart
	 * @param selEnd
	 * @return
	 */
	private Integer getCommonFontSize(Editable editable, int selStart, int selEnd) {
		Integer size = null;
		if (selStart == selEnd) return null;
		for (int i = selStart; i < selEnd; i++) {
			AbsoluteSizeSpan[] spans = editable.getSpans(i, i + 1, AbsoluteSizeSpan.class);
			Integer thisSize = null;
			for (AbsoluteSizeSpan span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (i >= spanStart && i < spanEnd) {
					thisSize = span.getSize();
					break;
				}
			}
			if (thisSize == null) return null;
			if (size == null) size = thisSize;
			else if (!size.equals(thisSize)) return null;
		}
		return size;
	}

	/**
	 * [光标模式] 获取指定位置字符的前景色（字体颜色）。
	 * 用于无选区时，判断光标处的文字颜色对应工具栏高亮和样式展示。
	 * @param editable 富文本内容
	 * @param pos 待检查字符的起始位置（推荐用光标左侧字符下标）
	 * @return 若当前位置有ForegroundColorSpan，返回其颜色值；否则返回null
	 */
	private Integer getColorOnPos(Editable editable, int pos) {
		ForegroundColorSpan[] spans = editable.getSpans(pos, pos + 1, ForegroundColorSpan.class);
		for(ForegroundColorSpan span: spans){
			int start = editable.getSpanStart(span);
			int end = editable.getSpanEnd(span);
			if(pos >= start && pos < end) return span.getForegroundColor();
		}
		return null;
	}

	/**
	 * [光标模式] 获取指定位置字符的背景色。
	 * 用于无选区时，判断光标处的背景高亮显示（如背景色按钮高亮及样式恢复）。
	 * @param editable 富文本内容
	 * @param pos 待检查字符的起始位置
	 * @return 若当前位置有BackgroundColorSpan，返回颜色值；否则返回null
	 */
	private Integer getBgColorOnPos(Editable editable, int pos) {
		BackgroundColorSpan[] spans = editable.getSpans(pos, pos + 1, BackgroundColorSpan.class);
		for(BackgroundColorSpan span: spans){
			int start = editable.getSpanStart(span);
			int end = editable.getSpanEnd(span);
			if(pos >= start && pos < end) return span.getBackgroundColor();
		}
		return null;
	}

	/**
	 * [光标模式] 获取指定位置字符的字号（字体大小）。
	 * 用于无选区时，判断光标处的字体大小设置，配合字体大小工具栏按钮的高亮和数值显示用。
	 * @param editable 富文本内容
	 * @param pos 待检查字符的起始位置
	 * @return 若当前位置有AbsoluteSizeSpan，返回字号数值（单位px）；否则返回null
	 */
	private Integer getFontSizeOnPos(Editable editable, int pos) {
		AbsoluteSizeSpan[] spans = editable.getSpans(pos, pos + 1, AbsoluteSizeSpan.class);
		for(AbsoluteSizeSpan span: spans){
			int start = editable.getSpanStart(span);
			int end = editable.getSpanEnd(span);
			if(pos >= start && pos < end) return span.getSize();
		}
		return null;
	}
	// #End of method:: onSelectionChanged

	/**
	 * 触摸事件，支持判断点击代办item时的特殊处理等
	 * @param event The motion event.
	 * @return
	 */
	@SuppressLint("ClickableViewAccessibility")
	@Override
	public boolean onTouchEvent(MotionEvent event) {
		if (!hasFocus() && ARE_Upcoming.isTouchSpan(event, this)) {
//            if (mNote != null) mNote.setDirty(true);
			if (onContentChangedListener != null) {
				onContentChangedListener.onContentChanged(getText());
			}
			return true;//没有焦点的时候，点击代办，拦截掉事件
		}

		boolean result = super.onTouchEvent(event);

		if (event.getAction() == MotionEvent.ACTION_UP && mShouldFixCursor) {
			mShouldFixCursor = false;
			int selection = getSelectionEnd();
			if (selection != mSelection) {
				if (mSelection > getText().length()) {
					mSelection = getText().length();
				}
				if (mSelection != -1) {
					setSelection(mSelection);
				} else {
//				    Logger.e("mSelection is -1");
				}
			}
		}

		// 在触摸事件结束后修正列表行的光标位置
		if (event.getAction() == MotionEvent.ACTION_UP) {
			post(new Runnable() {
				@Override
				public void run() {
					fixListLineCursorPosition();
				}
			});
		}

		return result;
	}

	/**
	 * 某些场景下强制修正光标位置（比如样式切换后保证无异常）
	 * @param selection
	 */
	public void shouldFixCursor(int selection) {
		mShouldFixCursor = true;
		mSelection = selection;
	}

	/**
	 * 修正列表行中的光标位置
	 * 当光标位于零宽字符位置时，将其移动到零宽字符之后
	 * 修复了有序列表和无序列表光标位置不正确的问题
	 */
	private void fixListLineCursorPosition() {
		try {
			Editable editable = getEditableText();
			if (editable == null) return;

			int cursorPosition = getSelectionStart();
			if (cursorPosition < 0) return;

			Layout layout = getLayout();
			if (layout == null) return;

			// 处理光标在文本末尾的情况
			if (cursorPosition >= editable.length()) {
				if (editable.length() > 0) {
					cursorPosition = editable.length() - 1;
				} else {
					return;
				}
			}

			int line = layout.getLineForOffset(cursorPosition);
			int lineStart = layout.getLineStart(line);
			int lineEnd = layout.getLineEnd(line);

			// 检查该行是否有列表项span
			UpcomingListSpan[] upcomingSpans = editable.getSpans(lineStart, lineEnd, UpcomingListSpan.class);
			ListNumberSpan[] numberSpans = editable.getSpans(lineStart, lineEnd, ListNumberSpan.class);
			ListBulletSpan[] bulletSpans = editable.getSpans(lineStart, lineEnd, ListBulletSpan.class);

			boolean hasListSpan = (upcomingSpans != null && upcomingSpans.length > 0) ||
					(numberSpans != null && numberSpans.length > 0) ||
					(bulletSpans != null && bulletSpans.length > 0);

			// 分析整个文本的零宽字符分布
			StringBuilder fullText = new StringBuilder();
			for (int i = 0; i < Math.min(editable.length(), 50); i++) {
				char c = editable.charAt(i);
				if (c == Constants.ZERO_WIDTH_SPACE_INT) {
					fullText.append("[ZWS]");
				} else if (c == '\n') {
					fullText.append("\\n");
				} else {
					fullText.append(c);
				}
			}
			Logger.d("AREditText", "fixListLineCursorPosition: fullText(first 50 chars)='" + fullText.toString() + "'");
			Logger.d("AREditText", "fixListLineCursorPosition: line=" + line + ", lineStart=" + lineStart + ", lineEnd=" + lineEnd + ", hasListSpan=" + hasListSpan + ", cursor=" + cursorPosition);
			if (upcomingSpans != null && upcomingSpans.length > 0) {
				Logger.d("AREditText", "fixListLineCursorPosition: found UpcomingListSpan, count=" + upcomingSpans.length);
			}
			if (numberSpans != null && numberSpans.length > 0) {
				Logger.d("AREditText", "fixListLineCursorPosition: found ListNumberSpan, count=" + numberSpans.length);
			}
			if (bulletSpans != null && bulletSpans.length > 0) {
				Logger.d("AREditText", "fixListLineCursorPosition: found ListBulletSpan, count=" + bulletSpans.length);
			}

			if (hasListSpan) {
				// 检查该行是否只有零宽字符（空白列表行）
				boolean isEmptyListLine = true;
				StringBuilder lineContent = new StringBuilder();
				for (int i = lineStart; i < lineEnd; i++) {
					if (i < editable.length()) {
						char c = editable.charAt(i);
						lineContent.append(c == Constants.ZERO_WIDTH_SPACE_INT ? "[ZWS]" : c);
						if (c != Constants.ZERO_WIDTH_SPACE_INT && c != '\n') {
							isEmptyListLine = false;
							break;
						}
					}
				}

				Logger.d("AREditText", "fixListLineCursorPosition: lineContent='" + lineContent.toString() + "', isEmptyListLine=" + isEmptyListLine);

				// 修复逻辑：无论是空行还是有内容的行，都要检查光标是否在零宽字符位置
				boolean needsFix = false;
				int targetPosition = -1;

				if (isEmptyListLine) {
					// 对于空白列表行，将光标定位到零宽字符之后
					for (int i = lineStart; i < lineEnd && i < editable.length(); i++) {
						if (editable.charAt(i) == Constants.ZERO_WIDTH_SPACE_INT) {
							targetPosition = i + 1;
							needsFix = (getSelectionStart() != targetPosition);
							Logger.d("AREditText", "fixListLineCursorPosition: empty line, found zero-width char at " + i + ", target position=" + targetPosition);
							break;
						}
					}
				} else {
					// 对于有内容的列表行，检查光标是否在零宽字符位置
					if (cursorPosition < editable.length() &&
						editable.charAt(cursorPosition) == Constants.ZERO_WIDTH_SPACE_INT) {
						targetPosition = cursorPosition + 1;
						needsFix = true;
						Logger.d("AREditText", "fixListLineCursorPosition: non-empty line, cursor on zero-width char, target position=" + targetPosition);
					}
					// 额外检查：如果光标在行首且行首有零宽字符，也需要修正
					else if (cursorPosition == lineStart && lineStart < editable.length() &&
							 editable.charAt(lineStart) == Constants.ZERO_WIDTH_SPACE_INT) {
						targetPosition = lineStart + 1;
						needsFix = true;
						Logger.d("AREditText", "fixListLineCursorPosition: cursor at line start with zero-width char, target position=" + targetPosition);
					}
				}

				// 执行光标修正
				if (needsFix && targetPosition > 0 && targetPosition <= editable.length()) {
					setSelection(targetPosition);
					Logger.d("AREditText", "fixListLineCursorPosition: cursor moved from " + cursorPosition + " to " + targetPosition);
				} else {
					Logger.d("AREditText", "fixListLineCursorPosition: no fix needed, needsFix=" + needsFix + ", targetPosition=" + targetPosition);
				}
			}
		} catch (Exception e) {
			Logger.e("AREditText", "Error fixing list line cursor position: " + e.getMessage());
		}
	}

	/**
	 * 覆写剪贴板菜单：拦截粘贴为html富文本时的特定场景，强制文本模式
	 * @param id
	 * @return
	 */
	@Override
	public boolean onTextContextMenuItem(int id) {
		if (id == android.R.id.copy) {
			if (mOnCopyListener != null) {
				int start = getSelectionStart();
				int end = min(getSelectionEnd(), getText().length());
				CharSequence text = Objects.requireNonNull(getText()).subSequence(start, end);
				mOnCopyListener.onCopy(start, end, text);
			}
			return true;
		}
		// 1. 优先检查最大长度超限禁止粘贴（防抖：InputFilter仅对逐字符生效，粘贴需要全量判断）
		if (id == android.R.id.paste || id == android.R.id.pasteAsPlainText) {
			ClipboardManager clipboard = (ClipboardManager) getContext().getSystemService(Context.CLIPBOARD_SERVICE);
			ClipData clip = clipboard != null ? clipboard.getPrimaryClip() : null;
			String pasteStr = "";
			if (clip != null && clip.getItemCount() > 0) {
				CharSequence cs = clip.getItemAt(0).coerceToText(getContext());
				if (cs != null) pasteStr = cs.toString();
			}
			int curLen = getText() != null ? getText().length() : 0;
			int selStart = getSelectionStart();
			int selEnd = getSelectionEnd();
			int selLen = Math.abs(selEnd - selStart);
			int wouldLength = curLen - selLen + pasteStr.length();
			if (wouldLength > maxContentLength) {
				// 粘贴后将超限，禁止粘贴
				if (onLimitListener != null) onLimitListener.onPasteLimit();
				// 截断不让粘贴
				return true;
			}
		}

		// 2. 拦截HTML富文本粘贴，强制以纯文本方式粘贴
		ClipboardManager clipboard = (ClipboardManager) getContext().getSystemService(Context.CLIPBOARD_SERVICE);
		ClipData clip = clipboard != null ? clipboard.getPrimaryClip() : null;
		Logger.d("AREditText","onTextContextMenuItem id=" + id + " --- " + (clip == null ? null : clip.getDescription().getMimeType(0)) );
		//not parse html when paste
		if (clip != null
				&& clip.getDescription() != null
				&& ClipDescription.MIMETYPE_TEXT_HTML.equals(clip.getDescription().getMimeType(0))
				&& id == android.R.id.paste) {
			// 强制转为纯文本粘贴，不走富文本，否则可能解析引擎或样式重复
			id = android.R.id.pasteAsPlainText;
		}
		// 3. 正常走系统粘贴逻辑
		return super.onTextContextMenuItem(id);
	}


	@Override
	protected void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
		setTextColor(ContextCompat.getColor(getContext(), R.color.text_title));
	}

	// 监控开关方法
	public boolean isSTORAGE_MONITORING(){ return STORAGE_MONITORING; }
	public void startStorageMonitor() { STORAGE_MONITORING = true; }
	public void stopStorageMonitor() { STORAGE_MONITORING = false; }

	public boolean isAPPLY_MONITORING() { return APPLY_MONITORING; }
	public void startApplyMonitor() { APPLY_MONITORING = true; }
	public void stopApplyMonitor() { APPLY_MONITORING = false; }

	public boolean isUISHOW_MONITORING() { return UISHOW_MONITORING; }
	public void startUiShowMonitor() { UISHOW_MONITORING = true; }
	public void stopUiSHowMonitor() { UISHOW_MONITORING = false; }

	public void startAllMonitor() {
		startStorageMonitor();
		startApplyMonitor();
		startUiShowMonitor();
	}
	public void stopAllMonitor() {
		stopStorageMonitor();
		stopApplyMonitor();
		stopUiSHowMonitor();
	}

	/**
	 * 设置/恢复行距
	 * @param value
	 */
	public void setLineSpaceExtra(float value) {
		//setLineSpacing(value,1.0f);
	}
	public void restoreDefaultLineSpace() {
		//setLineSpacing(mLineSpace,1.0f);
	}


	/**
	 * 在当前光标处插入文本，支持选中区覆盖
	 * @param text
	 */
	public void insertTextAtCursor(CharSequence text) {
		if (text == null || text.length() == 0) return;
		Editable editable = getEditableText();
		int start = getSelectionStart();
		int end = getSelectionEnd();
		if (start < 0 || end < 0) {
			editable.insert(editable.length(), text);
			setSelection(getText().length());
		} else {
			int min = Math.min(start, end);
			int max = Math.max(start, end);
			editable.replace(min, max, text);
			setSelection(min + text.length()); // 光标移到插入后
		}
	}


	/* ----------------------
	 * Customization part
	 * ---------------------- */

	/**
	 * 手动更新段落格式的按钮状态互斥(如只允许一种列表高亮)
	 * @param style
	 * @param status
	 */
	public void updateParagraphCheckStatus(IARE_Style style, boolean status) {
		if (status) {
			boolean upComingChecked = false;
			boolean listNumberChecked = false;
			boolean listBulletChecked = false;
			if (style instanceof ARE_Upcoming) {
				upComingChecked = true;
			} else if (style instanceof ARE_ListBullet) {
				listBulletChecked = true;
			} else if (style instanceof  ARE_ListNumber) {
				listNumberChecked = true;
			}
			for (IARE_Style item : sStylesList) {
				if (item instanceof ARE_Upcoming) {
					item.updateCheckStatus(upComingChecked);
				} else if (item instanceof ARE_ListBullet) {
					item.updateCheckStatus(listBulletChecked);
				} else if (item instanceof  ARE_ListNumber) {
					item.updateCheckStatus(listNumberChecked);
				}
			}
		} else {
			style.updateCheckStatus(false);
		}
	}

	/**
	 * 绑定Note
	 * @param note
	 */
	public void setNote(Note note) {
		mNote = note;
	}

	/**
	 * 兼容某些输入法/软键盘的定制InputConnection包装
	 * @param outAttrs Fill in with attribute information about the connection.
	 * @return
	 */
	@Override
	public InputConnection onCreateInputConnection(EditorInfo outAttrs) {
		return new InputConnectionCEWrapper(super.onCreateInputConnection(outAttrs));
	}

	public Runnable getSaveContentToMemoryTask() {
		return mSaveContentToMemoryTask;
	}

	public void setSaveContentToMemoryTask(Runnable saveContentToMemoryTask) {
		this.mSaveContentToMemoryTask = saveContentToMemoryTask;
	}

	/**
	 * 设置撤销/重做状态监听器
	 */
	public void setUndoRedoStateListener(UndoRedoStateListener listener) {
		this.mUndoRedoStateListener = listener;
	}

	/**
	 * 更新撤销/重做按钮状态
	 */
	public void updateUndoRedoState(boolean canUndo, boolean canRedo) {
		if (mUndoRedoStateListener != null) {
			mUndoRedoStateListener.onUndoRedoStateChanged(canUndo, canRedo);
		}
	}

	/**
	 * undo栈被添加元素，和实际undo，redo操作无关
	 */
	public void onUndoStackAdded(@NonNull RichTextEditOperation operation) {
		if (mUndoRedoStateListener != null) {
			mUndoRedoStateListener.onUndoStackAdded(operation);
		}
	}

	/**
	 * undo栈被移除元素，和实际undo，redo操作无关
	 */
	public void onUndoStackRemoved(@NonNull RichTextEditOperation operation) {
		if (mUndoRedoStateListener != null) {
			mUndoRedoStateListener.onUndoStackRemoved(operation);
		}
	}

	/**
	 * 执行撤销操作
	 * @return 是否成功撤销
	 */
	public boolean undo() {
		setMonitoring(false);
		boolean result = mRichTextUndoRedoManager.undo();
		setMonitoring(true);
		return result;
	}

	/**
	 * 执行重做操作
	 * @return 是否成功重做
	 */
	public boolean redo() {
		setMonitoring(false);
		boolean result = mRichTextUndoRedoManager.redo();
		setMonitoring(true);
		return result;
	}

	/**
	 * 清空历史记录
	 */
	public void clearHistory() {
		mRichTextUndoRedoManager.clearHistory();
	}

	/**
	 * 判断是否可以撤销
	 */
	public boolean canUndo() {
		return mRichTextUndoRedoManager.canUndo();
	}

	/**
	 * 判断是否可以重做
	 */
	public boolean canRedo() {
		return mRichTextUndoRedoManager.canRedo();
	}
	public RichTextUndoRedoManager getRichTextUndoRedoManager() {
		return mRichTextUndoRedoManager;
	}

	/**
	 * 撤销/重做状态监听器接口
	 */
	public interface UndoRedoStateListener {
		void onUndoRedoStateChanged(boolean canUndo, boolean canRedo);
		void onUndoStackAdded(@NonNull RichTextEditOperation newOperation);
		void onUndoStackRemoved(@NonNull RichTextEditOperation newOperation);
	}

	public interface OnCopyListener {
		void onCopy(int start, int end, CharSequence text);
		void startPaste(Editable editable, int start, int end, int before, int count, boolean isDelete);
	}

	/**
	 * 请求焦点并显示光标，支持指定光标位置
	 * 不强制弹起键盘
	 */
	public void requestFocusAndShowCursor() {
		requestFocusAndShowCursor(0, false); // 默认设置到开始位置
	}

	/**
	 * 请求焦点并显示光标
	 */
	public void requestFocusAndShowCursor(int cursorPosition, boolean showKeyboard) {
		if (isDestroyed) return;

		post(() -> {
			try {
				if (!hasFocus()) {
					setFocusable(true);
					setFocusableInTouchMode(true);
					requestFocus();

					// 设置光标到开始位置
					setSelection(cursorPosition);

					// 显示键盘
					if (showKeyboard) {
						InputMethodManager imm = (InputMethodManager) getContext()
								.getSystemService(Context.INPUT_METHOD_SERVICE);
						if (imm != null) {
							imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT);
						}
					}
				}else{
					// 已有焦点，直接设置光标位置
					int textLength = Objects.requireNonNull(getText()).length();
					int targetPosition = Math.min(Math.max(0, cursorPosition), textLength);
					setSelection(targetPosition);
				}
			} catch (Exception e) {
				Logger.e("AREditText", "requestFocusAndShowCursor error: " + e.getMessage());
			}
		});
	}

	public void requestFocusAndShowKeyboard() {
		if (isDestroyed) return;

		post(() -> {
			try {
				if (!hasFocus()) {
					setFocusable(true);
					setFocusableInTouchMode(true);
					requestFocus();
				}
				// 显示键盘
				InputMethodManager imm = (InputMethodManager) getContext()
						.getSystemService(Context.INPUT_METHOD_SERVICE);
				if (imm != null) {
					imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT);
				}
			} catch (Exception e) {
				Logger.e("AREditText", "requestFocusAndShowCursor error: " + e.getMessage());
			}
		});
	}

	/**
	 * 隐藏键盘并清除焦点
	 */
	public void hideKeyboardAndClearFocus() {
		if (isDestroyed) return;

		try {
			// 隐藏键盘
			InputMethodManager imm = (InputMethodManager) getContext()
					.getSystemService(Context.INPUT_METHOD_SERVICE);
			if (imm != null) {
				imm.hideSoftInputFromWindow(getWindowToken(), 0);
			}

			// 清除焦点
			clearFocus();
			setFocusable(false);
			setFocusableInTouchMode(false);
		} catch (Exception e) {
			Logger.e("AREditText", "hideKeyboardAndClearFocus error: " + e.getMessage());
		}
	}

	public void clearEditTextFocus() {
		clearFocus();
		setFocusable(false);
		setFocusableInTouchMode(false);
	}

	public void initUndoState(Spannable text) {
		mRichTextUndoRedoManager.initState(text);
	}

	/**
	 * 完整清理资源
	 */
	public void destroy() {
		if (isDestroyed) return;
		isDestroyed = true;

		Logger.d("AREditText", "Destroying AREditText: " + this.hashCode());

		// 停止所有监控
		stopAllMonitor();

		// 清理样式列表
		clearStylesList();

		// 清理历史记录
		clearHistory();

		// 清理监听器
		setOnContentChangedListener(null);
		setOnSelectionChangedListener(null);
		setStyleStatusListener(null);
		setOnClickListener(null);
		setOnMeasureChanged(null);
		setUndoRedoStateListener(null);
		setonSelectionChanged(null);
		setOnCursorToScreenListener(null);

		// 清理TextWatcher
		if (mTextWatcher != null) {
			removeTextChangedListener(mTextWatcher);
			mTextWatcher = null;
		}

		// 清理其他引用
		mNote = null;
		mSaveContentToMemoryTask = null;
	}


	/**
	 * 分析删除范围，确定需要删除哪些列表span
	 * 根据删除范围是否跨越多个内容块来决定删除策略
	 */
	private void analyzeDeleteRangeAndRecordSpans(CharSequence s, int deleteStart, int deleteCount) {
		Editable editable = (Editable) s;
		int deleteEnd = deleteStart + deleteCount;

		// 获取删除范围内的所有行
		Layout layout = getLayout();
		if (layout == null) {
			return;
		}

		int startLine = layout.getLineForOffset(deleteStart);
		int endLine = layout.getLineForOffset(Math.min(deleteEnd - 1, s.length() - 1));

		// 如果删除范围在同一行内，检查是否选中了列表项的全部文本内容
		if (startLine == endLine) {
			// 单行内删除，检查是否需要删除列表前缀
			handleSingleLineListDeletion(editable, layout, deleteStart, deleteEnd, startLine, deleteCount);
			return;
		}

		// 跨行删除，需要分析每一行的情况
		for (int line = startLine; line <= endLine; line++) {
			int lineStart = layout.getLineStart(line);
			int lineEnd = layout.getLineEnd(line);

			// 计算删除范围与当前行的交集
			int intersectionStart = Math.max(deleteStart, lineStart);
			int intersectionEnd = Math.min(deleteEnd, lineEnd);

			if (intersectionStart >= intersectionEnd) {
				continue; // 没有交集
			}

			// 检查当前行是否有列表span
			Object[] listSpans = getListSpansInRange(editable, lineStart, lineEnd);
			if (listSpans.length == 0) {
				continue; // 当前行不是列表项
			}

			if (line == startLine) {
				// 第一行：检查删除范围是否包含该行的全部文本内容
				if (isSelectionCoveringEntireListContent(editable, intersectionStart, intersectionEnd, lineStart, lineEnd)) {
					// 删除范围包含该行的全部文本内容，删除前缀
					for (Object span : listSpans) {
						spansToRemoveAfterDelete.add(span);
					}
				}
				// 否则保留第一行的前缀
			} else if (line == endLine) {
				// 最后一行：检查删除范围是否包含该行的全部文本内容
				if (isSelectionCoveringEntireListContent(editable, intersectionStart, intersectionEnd, lineStart, lineEnd)) {
					// 删除范围包含该行的全部文本内容，删除前缀
					for (Object span : listSpans) {
						spansToRemoveAfterDelete.add(span);
					}
				}
				// 否则保留最后一行的前缀，内容会合并到第一行
			} else {
				// 中间行：完全包含在删除范围内，删除前缀
				for (Object span : listSpans) {
					spansToRemoveAfterDelete.add(span);
				}
			}
		}
	}

	/**
	 * 处理单行内的列表删除逻辑
	 * 当选区范围包含列表项的全部文本内容时，删除列表前缀
	 */
	private void handleSingleLineListDeletion(Editable editable, Layout layout, int deleteStart, int deleteEnd, int line, int deleteCount) {
		int lineStart = layout.getLineStart(line);
		int lineEnd = layout.getLineEnd(line);

		// 检查当前行是否有列表span
		Object[] listSpans = getListSpansInRange(editable, lineStart, lineEnd);
		if (listSpans.length == 0) {
			return; // 当前行不是列表项
		}

		// 如果只删除一个字符（通常是按退格键），不删除列表前缀
		// 用户需要先删除所有文字，然后再按一次删除键才删除前缀
		if (deleteCount == 1) {
			return;
		}

		// 检查删除范围是否包含列表项的全部文本内容（多字符删除或选区删除）
		if (isSelectionCoveringEntireListContent(editable, deleteStart, deleteEnd, lineStart, lineEnd)) {
			// 删除范围包含列表项的全部文本内容，删除前缀
			for (Object span : listSpans) {
				spansToRemoveAfterDelete.add(span);
			}
		}
	}

	/**
	 * 检查选区是否覆盖了列表项的全部文本内容
	 * 列表项通常以零宽字符开始，我们需要检查选区是否包含了除零宽字符外的所有内容
	 */
	private boolean isSelectionCoveringEntireListContent(Editable editable, int selStart, int selEnd, int lineStart, int lineEnd) {
		// 如果行结束位置有换行符，不包括换行符
		if (lineEnd > lineStart && lineEnd <= editable.length() && editable.charAt(lineEnd - 1) == '\n') {
			lineEnd--;
		}

		// 找到行内实际文本内容的开始位置（跳过零宽字符）
		int contentStart = lineStart;
		while (contentStart < lineEnd && contentStart < editable.length() &&
				editable.charAt(contentStart) == Constants.ZERO_WIDTH_SPACE_INT) {
			contentStart++;
		}

		// 如果行内没有实际文本内容（只有零宽字符或为空），不删除前缀
		if (contentStart >= lineEnd) {
			return false;
		}

		// 计算实际文本内容的长度（排除零宽字符）
		int actualContentLength = 0;
		for (int i = contentStart; i < lineEnd; i++) {
			if (i < editable.length() && editable.charAt(i) != Constants.ZERO_WIDTH_SPACE_INT) {
				actualContentLength++;
			}
		}

		// 如果没有实际内容，不删除前缀
		if (actualContentLength == 0) {
			return false;
		}

		// 计算选区内实际文本内容的长度（排除零宽字符）
		int selectedContentLength = 0;
		int effectiveSelStart = Math.max(selStart, contentStart);
		int effectiveSelEnd = Math.min(selEnd, lineEnd);

		for (int i = effectiveSelStart; i < effectiveSelEnd; i++) {
			if (i < editable.length() && editable.charAt(i) != Constants.ZERO_WIDTH_SPACE_INT) {
				selectedContentLength++;
			}
		}

		// 如果选区包含了所有实际文本内容，则认为应该删除前缀
		// 允许有小的误差（比如选区可能稍微超出或不足）
		return selectedContentLength >= actualContentLength;
	}

	/**
	 * 获取指定范围内的所有列表span
	 */
	private Object[] getListSpansInRange(Editable editable, int start, int end) {
		java.util.List<Object> allSpans = new java.util.ArrayList<>();

		UpcomingListSpan[] upcomingSpans = editable.getSpans(start, end, UpcomingListSpan.class);
		if (upcomingSpans != null) {
			for (UpcomingListSpan span : upcomingSpans) {
				allSpans.add(span);
			}
		}

		ListBulletSpan[] bulletSpans = editable.getSpans(start, end, ListBulletSpan.class);
		if (bulletSpans != null) {
			for (ListBulletSpan span : bulletSpans) {
				allSpans.add(span);
			}
		}

		ListNumberSpan[] numberSpans = editable.getSpans(start, end, ListNumberSpan.class);
		if (numberSpans != null) {
			for (ListNumberSpan span : numberSpans) {
				allSpans.add(span);
			}
		}

		return allSpans.toArray();
	}

	/**
	 * 设置列表span的范围
	 */
	private void setListSpan(Editable editable, Object listSpan, int start, int end) {
		if (listSpan instanceof UpcomingListSpan) {
			editable.setSpan(listSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		} else if (listSpan instanceof ListBulletSpan) {
			editable.setSpan(listSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		} else if (listSpan instanceof ListNumberSpan) {
			editable.setSpan(listSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		}
	}

	/**
	 * 检查删除操作是否会导致内容合并到前一行
	 */
	private boolean checkIfContentWillMerge(int deletePosition) {
		try {
			Editable editable = getEditableText();
			if (editable == null || deletePosition <= 0) {
				return false;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return false;
			}

			// 检查当前行是否有内容（除了列表前缀）
			int currentLine = layout.getLineForOffset(deletePosition);
			int lineStart = layout.getLineStart(currentLine);
			int lineEnd = layout.getLineEnd(currentLine);

			// 检查除了零宽字符外是否还有其他内容（包括换行符）
			boolean hasRealContent = false;
			for (int i = lineStart; i < lineEnd; i++) {
				char c = editable.charAt(i);
				if (c != Constants.ZERO_WIDTH_SPACE_INT) {
					hasRealContent = true;
					break;
				}
			}

			// 特殊情况：如果当前行只有零宽字符和换行符，但下一行有内容，也认为会发生合并
			if (!hasRealContent && lineEnd < editable.length() && editable.charAt(lineEnd) == '\n') {
				// 检查下一行是否有内容
				if (currentLine + 1 < layout.getLineCount()) {
					int nextLineStart = layout.getLineStart(currentLine + 1);
					int nextLineEnd = layout.getLineEnd(currentLine + 1);

					// 如果下一行末有换行符，不包含换行符
					if (nextLineEnd > nextLineStart && nextLineEnd <= editable.length() &&
							editable.charAt(nextLineEnd - 1) == '\n') {
						nextLineEnd--;
					}

					// 检查下一行是否有实际内容
					for (int i = nextLineStart; i < nextLineEnd; i++) {
						char c = editable.charAt(i);
						if (c != Constants.ZERO_WIDTH_SPACE_INT) {
							hasRealContent = true;
							break;
						}
					}
				}
			}

			return hasRealContent; // 有实际内容，会合并到前一行
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 查找内容合并的目标列表span（前一行的列表span）
	 */
	private Object findTargetListSpanForMerge(int deletePosition) {
		try {
			Editable editable = getEditableText();
			if (editable == null || deletePosition <= 0) {
				return null;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return null;
			}

			// 找到前一行
			int currentLine = layout.getLineForOffset(deletePosition);
			if (currentLine <= 0) {
				return null; // 已经是第一行
			}

			int prevLine = currentLine - 1;
			int prevLineStart = layout.getLineStart(prevLine);
			int prevLineEnd = layout.getLineEnd(prevLine);

			// 查找前一行的列表span
			UpcomingListSpan[] upcomingSpans = editable.getSpans(prevLineStart, prevLineEnd, UpcomingListSpan.class);
			if (upcomingSpans != null && upcomingSpans.length > 0) {
				return upcomingSpans[0];
			}

			ListBulletSpan[] bulletSpans = editable.getSpans(prevLineStart, prevLineEnd, ListBulletSpan.class);
			if (bulletSpans != null && bulletSpans.length > 0) {
				return bulletSpans[0];
			}

			ListNumberSpan[] numberSpans = editable.getSpans(prevLineStart, prevLineEnd, ListNumberSpan.class);
			if (numberSpans != null && numberSpans.length > 0) {
				return numberSpans[0];
			}

			return null;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 在内容合并后扩展列表span范围
	 */
	private void expandListSpanAfterMerge(Editable editable, Object listSpan) {
		if (listSpan == null) {
			return;
		}

		// 获取当前span的范围
		int spanStart = editable.getSpanStart(listSpan);
		int spanEnd = editable.getSpanEnd(listSpan);

		if (spanStart < 0 || spanEnd < 0) {
			return;
		}

		// 使用Layout来准确计算span应该覆盖的完整范围
		Layout layout = getLayout();
		if (layout != null) {
			try {
				// 找到span开始位置所在的行
				int startLine = layout.getLineForOffset(spanStart);
				int startLineStart = layout.getLineStart(startLine);

				// 计算新的span结束位置：从span开始位置找到下一个换行符或文本结束
				int textLength = editable.length();
				int newSpanEnd = spanStart;

				while (newSpanEnd < textLength) {
					char c = editable.charAt(newSpanEnd);
					if (c == '\n') {
						break; // 找到换行符，不包含换行符本身
					}
					newSpanEnd++;
				}

				// 确保新的结束位置不小于原来的结束位置（防止span范围缩小）
				if (newSpanEnd < spanEnd) {
					newSpanEnd = spanEnd;
				}

				// 检查是否需要扩展span范围
				boolean needsExpansion = (spanStart > startLineStart) || (spanEnd < newSpanEnd);

				if (needsExpansion) {
					// 移除原span
					editable.removeSpan(listSpan);

					// 重新设置span范围：从行开始到合并后的内容结束
					setListSpan(editable, listSpan, startLineStart, newSpanEnd);

					// 强制刷新工具栏状态
					post(new Runnable() {
						@Override
						public void run() {
							int currentSelection = getSelectionStart();
							onSelectionChanged(currentSelection, currentSelection);
						}
					});
				}
			} catch (Exception e) {
				// 忽略异常
			}
		} else {
			// 如果Layout为null，使用原来的逻辑作为备用
			// 计算新的span结束位置（到下一个换行符或文本结束）
			int textLength = editable.length();
			int newSpanEnd = spanStart;

			while (newSpanEnd < textLength) {
				char c = editable.charAt(newSpanEnd);
				if (c == '\n') {
					break; // 找到换行符，不包含换行符本身
				}
				newSpanEnd++;
			}

			// 强制扩展span范围，即使newSpanEnd不大于spanEnd
			if (newSpanEnd != spanEnd) {

				// 移除原span
				editable.removeSpan(listSpan);

				// 重新设置span范围
				setListSpan(editable, listSpan, spanStart, newSpanEnd);

				// 强制刷新工具栏状态
				post(new Runnable() {
					@Override
					public void run() {
						int currentSelection = getSelectionStart();
						onSelectionChanged(currentSelection, currentSelection);
					}
				});
			}
		}
	}

	/**
	 * 扩展列表span的范围以包含合并的内容
	 * 当删除操作导致内容合并到列表项时，需要扩展span范围
	 */
	private void expandListSpanToIncludeMergedContent(Editable editable, Object listSpan, int deletePosition) {
		// 获取当前span的范围
		int currentSpanStart = editable.getSpanStart(listSpan);
		int currentSpanEnd = editable.getSpanEnd(listSpan);

		if (currentSpanStart < 0 || currentSpanEnd < 0) {
			return;
		}

		// 找到span开始位置所在行的结束位置（合并后的行）
		// 不依赖Layout，直接从span开始位置查找到下一个换行符或文本结束
		int newSpanEnd = currentSpanStart;
		int textLength = editable.length();

		while (newSpanEnd < textLength) {
			char c = editable.charAt(newSpanEnd);
			if (c == '\n') {
				break; // 找到换行符，不包含换行符本身
			}
			newSpanEnd++;
		}

		// 如果需要扩展span范围
		if (newSpanEnd > currentSpanEnd) {

			// 移除原span
			editable.removeSpan(listSpan);

			// 重新设置span范围，覆盖整个合并后的行
			setListSpan(editable, listSpan, currentSpanStart, newSpanEnd);
		}

		// 延迟执行强制修复，确保所有删除操作完成后再处理
		post(new Runnable() {
			@Override
			public void run() {
				forceFixListSpanRange(editable, listSpan);
			}
		});
	}

	/**
	 * 强制修复列表span范围，确保span覆盖整个合并后的行
	 * 这是一个保险措施，用于处理常规扩展可能遗漏的情况
	 */
	private void forceFixListSpanRange(Editable editable, Object listSpan) {
		// 再次检查span是否还存在
		int spanStart = editable.getSpanStart(listSpan);
		int spanEnd = editable.getSpanEnd(listSpan);

		if (spanStart < 0 || spanEnd < 0) {
			return; // span已被删除
		}

		// 直接从span开始位置计算到行尾
		int textLength = editable.length();
		int correctSpanEnd = spanStart;

		while (correctSpanEnd < textLength) {
			char c = editable.charAt(correctSpanEnd);
			if (c == '\n') {
				break; // 找到换行符，不包含换行符本身
			}
			correctSpanEnd++;
		}

		// 如果当前span范围不正确，重新设置
		if (correctSpanEnd != spanEnd) {

			// 移除原span
			editable.removeSpan(listSpan);

			// 重新设置正确的span范围
			setListSpan(editable, listSpan, spanStart, correctSpanEnd);

			// 强制刷新工具栏状态
			post(new Runnable() {
				@Override
				public void run() {
					int currentSelection = getSelectionStart();
					onSelectionChanged(currentSelection, currentSelection);
				}
			});
		}
	}

	/**
	 * 修复所有列表span的范围，确保它们正确覆盖各自的行
	 * 这是一个通用的修复方法，用于处理删除操作后可能出现的span范围问题
	 */
	private void fixAllListSpanRangesAfterDelete() {
		try {
			Editable editable = getEditableText();
			if (editable == null) {
				return;
			}

			Layout layout = getLayout();
			if (layout == null) {
				return;
			}

			// 获取所有列表span并直接修复，而不是按行处理
			// 这样可以更好地处理跨多行的列表项
			UpcomingListSpan[] upcomingSpans = editable.getSpans(0, editable.length(), UpcomingListSpan.class);
			ListBulletSpan[] bulletSpans = editable.getSpans(0, editable.length(), ListBulletSpan.class);
			ListNumberSpan[] numberSpans = editable.getSpans(0, editable.length(), ListNumberSpan.class);

			// 修复待办事项span
			for (UpcomingListSpan span : upcomingSpans) {
				fixSpanToWholeLine(editable, span);
			}

			// 修复无序列表span
			for (ListBulletSpan span : bulletSpans) {
				fixSpanToWholeLine(editable, span);
			}

			// 修复有序列表span
			for (ListNumberSpan span : numberSpans) {
				fixSpanToWholeLine(editable, span);
			}

			// 强制刷新工具栏状态
			int currentSelection = getSelectionStart();
			onSelectionChanged(currentSelection, currentSelection);
		} catch (Exception e) {
			Logger.e("AREditText", "Error : " + e.getMessage());
		}
	}

	/**
	 * 修复单个span使其覆盖完整的列表项内容
	 * 对于跨多行的列表项，需要确保span覆盖从列表开始到内容结束的完整范围
	 */
	private void fixSpanToWholeLine(Editable editable, Object span) {
		try {
			int currentSpanStart = editable.getSpanStart(span);
			int currentSpanEnd = editable.getSpanEnd(span);

			if (currentSpanStart < 0 || currentSpanEnd < 0) {
				return; // span已被删除
			}

			// 对于列表span，需要确保它从列表项开始位置覆盖到内容的实际结束位置
			// 而不是仅仅覆盖当前行

			// 找到列表项的实际开始位置（通常是零宽字符的位置）
			int actualStart = currentSpanStart;

			// 找到列表项内容的实际结束位置（到下一个换行符或文本结束）
			int textLength = editable.length();
			int actualEnd = currentSpanStart;

			while (actualEnd < textLength) {
				char c = editable.charAt(actualEnd);
				if (c == '\n') {
					break; // 找到换行符，不包含换行符本身
				}
				actualEnd++;
			}

			// 确保新的结束位置不小于原来的结束位置
			actualEnd = Math.max(actualEnd, currentSpanEnd);

			// 检查span是否需要修复
			boolean needsFix = (currentSpanStart != actualStart) || (currentSpanEnd != actualEnd);

			if (needsFix) {
				// 移除原span
				editable.removeSpan(span);

				// 重新设置span范围覆盖完整的列表项内容
				setListSpan(editable, span, actualStart, actualEnd);
			}
		} catch (Exception e) {
			Logger.e("AREditText", "Error : " + e.getMessage());
		}
	}
}
