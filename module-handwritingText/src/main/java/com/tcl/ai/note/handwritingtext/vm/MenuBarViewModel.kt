package com.tcl.ai.note.handwritingtext.vm

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.bean.BrushMenu
import com.tcl.ai.note.handwritingtext.bean.BrushType
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.StrokeStyle
import com.tcl.ai.note.handwritingtext.bean.toBrushType
import com.tcl.ai.note.handwritingtext.intent.RichTextIntent
import com.tcl.ai.note.utils.ComposableToast
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

/**
 * 菜单栏ViewModel - 统一管理所有菜单栏状态和事件
 */
class MenuBarViewModel : ViewModel() {

    // 是否显示展开控件
    var showExpandButton by mutableStateOf(false)

    // 菜单栏状态
    private val _menuBarState = MutableStateFlow(MenuBarUiState())
    val menuBarState: StateFlow<MenuBarUiState> = _menuBarState.asStateFlow()
    
    // 当前菜单类型 - 为外部提供响应式监听
    private val _currentMenuType = MutableStateFlow(MenuBar.KEYBOARD)
    val currentMenuType: StateFlow<MenuBar> = _currentMenuType.asStateFlow()

    // 历史记录 - 用于撤销和重做
    private val textEditHistory = mutableListOf<String>()
    private val drawingHistory = mutableListOf<String>() 
    private var textHistoryPosition = -1
    private var drawingHistoryPosition = -1

    // 通用回调接口
    var onCommonDrawingModeAction: (() -> Unit)? = null
    var onTextEditRequested: (() -> Unit)? = null
    var onDrawingEditRequested: ((strokeStyle: StrokeStyle) -> Unit)? = null
    var onUndoRequested: (() -> Unit)? = null
    var onRedoRequested: (() -> Unit)? = null
    var onColorSelected: ((Color) -> Unit)? = null

    // 增加一个字段存储 RichTextViewModel 的引用
    private var richTextViewModel: RichTextViewModel? = null
    private var drawBoardViewModel: DrawBoardViewModel? = null

    
    init {
        // 初始化当前菜单类型
        updateCurrentMenuType(_menuBarState.value.currentMenuType)
    }
    
    /**
     * 测试方法：手动触发状态变化
     */
    fun testTriggerStateChange() {
        _menuBarState.update { currentState ->
            val testState = currentState.copy(canUndo = !currentState.canUndo) // 简单改变一个字段
            testState
        }
    }
    
    /**
     * 更新当前菜单类型状态
     */
    private fun updateCurrentMenuType(menuType: MenuBar) {
        _currentMenuType.value = menuType
    }
    
    /**
     * 执行绘图模式下的通用操作
     * 在切换到任何绘图相关功能时调用
     */
    private fun performCommonDrawingModeActions() {
        // 调用通用回调，让外部执行清除焦点、隐藏drawer等操作
        onCommonDrawingModeAction?.invoke()
    }

    /**
     * 获取当前菜单类型 - 让外部可以直接感知当前一级菜单
     * 
     * @return 当前菜单类型
     */
    fun getCurrentMenuType(): MenuBar = _menuBarState.value.currentMenuType
    
    /**
     * 检查当前是否处于绘图编辑模式
     * 
     * @return 如果当前处于绘图编辑模式，返回true；否则返回false
     */
    fun isDrawingMode(): Boolean = _currentMenuType.value == MenuBar.BRUSH

    /**
     * 重置所有按钮状态 - 确保同一时间只有一个按钮被选中
     */
    private fun resetAllButtonStates() {
        _menuBarState.update { currentState ->
            currentState.copy(
                isKeyboardActive = true,
                isBrushActive = false,
                isEraserActive = false,
                isLassoActive = false,
                isRulerActive = false,  // 保留直尺的状态，因为它可以和其他工具共存
                isHandwritingToTextActive = false,
                isAiActive = false,
                isBeautifyActive = false
            )
        }
    }

    /**
     * 重置绘图类按钮状态 - 保留直尺状态
     */
    private fun resetDrawingButtonStates() {
        _menuBarState.update { currentState ->
            currentState.copy(
                isKeyboardActive = false,
                isBrushActive = false,
                isEraserActive = false,
                isLassoActive = false,
                // 不重置直尺状态，让它可以与笔刷共存
                isHandwritingToTextActive = false,
                isAiActive = false,
                isBeautifyActive = false
            )
        }
    }


    /**
     * 设置 RichTextViewModel 引用
     */
    fun setRichTextViewModel(viewModel: RichTextViewModel, drawBoardViewModel: DrawBoardViewModel) {
        this.richTextViewModel = viewModel
        this.drawBoardViewModel = drawBoardViewModel
    }

    /**
     * 切换到文本编辑模式
     */
    fun switchToTextEditMode() {
        val menuType = MenuBar.KEYBOARD
        // 使用全重置方法，确保所有绘图工具状态都被清除，包括直尺
        resetAllButtonStates()
        
        _menuBarState.update { currentState ->
            currentState.copy(
                currentMenuType = menuType,
                isKeyboardActive = true,
                isRulerActive = false  // 确保直尺被禁用
            )
        }
        
        // 更新当前菜单类型状态
        updateCurrentMenuType(menuType)

        onTextEditRequested?.invoke()
    }

    /**
     * 切换到绘图模式（笔刷模式）
     */
    fun switchToDrawingMode() {
        val menuType = MenuBar.BRUSH
        // 使用新方法重置状态，保留直尺状态
        resetDrawingButtonStates()
        
        _menuBarState.update { currentState ->
            currentState.copy(
                currentMenuType = menuType,
                isBrushActive = true,
                isEraserActive = false,
                isPassiveEraserActive = false
            )
        }
        
        // 更新当前菜单类型状态
        updateCurrentMenuType(menuType)

        // 执行绘图模式下的通用操作
        performCommonDrawingModeActions()

        drawBoardViewModel?.let {
            onDrawingEditRequested?.invoke(it.strokeStyle)
        }
    }

    /**
     * 设置撤销重做功能的可用状态
     */
    fun setUndoRedoState(canUndo: Boolean, canRedo: Boolean) {
        _menuBarState.update { currentState ->
            currentState.copy(
                canUndo = canUndo,
                canRedo = canRedo
            )
        }
    }

    /**
     * 添加历史记录
     */
    fun addHistoryState(state: String, isDrawing: Boolean = false) {
        if (isDrawing) {
            // 如果在历史记录中间添加新状态，清除后面的记录
            if (drawingHistoryPosition < drawingHistory.size - 1) {
                drawingHistory.subList(drawingHistoryPosition + 1, drawingHistory.size).clear()
            }
            drawingHistory.add(state)
            drawingHistoryPosition = drawingHistory.size - 1
        } else {
            if (textHistoryPosition < textEditHistory.size - 1) {
                textEditHistory.subList(textHistoryPosition + 1, textEditHistory.size).clear()
            }
            textEditHistory.add(state)
            textHistoryPosition = textEditHistory.size - 1
        }
        
        updateUndoRedoState()
    }

    /**
     * 更新撤销重做按钮状态
     */
    private fun updateUndoRedoState() {
        val isDrawingMode = _menuBarState.value.currentMenuType == MenuBar.BRUSH
        val canUndo = if (isDrawingMode) drawingHistoryPosition > 0 else textHistoryPosition > 0
        val canRedo = if (isDrawingMode) drawingHistoryPosition < drawingHistory.size - 1 
                      else textHistoryPosition < textEditHistory.size - 1
        
        setUndoRedoState(canUndo, canRedo)
    }

    // 菜单项点击事件处理
    


    /**
     * 笔刷按钮点击 - 显示笔刷子菜单
     */
    fun onBrushClick() {
        // 设置当前菜单类型为钢笔 (BrushMenu.PEN)
        val brushMenuType = BrushMenu.PEN
        
        // 重置其他按钮状态但保留直尺状态
        resetDrawingButtonStates()
        
        // 设置笔刷为激活状态和菜单类型
        _menuBarState.update { currentState ->
            currentState.copy(
                isBrushActive = true,
                isEraserActive = false,
                currentMenuType = MenuBar.BRUSH,
                currentBrushType = drawBoardViewModel?.strokeStyle?.doodlePen?.toBrushType()?: BrushType.FOUNTAIN_PEN
            )
        }
        
        // 更新当前菜单类型状态
        updateCurrentMenuType(MenuBar.BRUSH)
        
        // 清除焦点 - 作为通用绘图模式操作的一部分
        performCommonDrawingModeActions()
        
        // 通知外部处理器切换笔刷抽屉的可见性
        // 直接调用 drawBoardViewModel.transformPenDrawerVisible() 的替代方案
        drawBoardViewModel?.let {
            onDrawingEditRequested?.invoke(it.strokeStyle)
        }

        // 如果有 RichTextViewModel 引用，直接更新其状态
        richTextViewModel?.let { viewModel ->
            viewModel.handleIntent(RichTextIntent.UpdateMenuType(MenuBar.BRUSH))
            viewModel.handleIntent(RichTextIntent.UpdateBrushMenuType(brushMenuType))
        }
    }
    
    /**
     * 选择具体笔刷类型
     */
    fun onSelectBrush(strokeStyle: StrokeStyle) {
        // 重置按钮状态但保留直尺状态
        resetDrawingButtonStates()
        
        // 更新UI状态
        _menuBarState.update { currentState ->
            currentState.copy(
                currentMenuType = MenuBar.BRUSH,
                currentBrushType = strokeStyle.doodlePen.toBrushType(),
                isBrushActive = true,
                isEraserActive = false,
                isLassoActive = false
            )
        }
        
        // 更新当前菜单类型状态
        updateCurrentMenuType(MenuBar.BRUSH)
        
        // 执行绘图模式下的通用操作
        performCommonDrawingModeActions()
        
        // 通知外部处理器
        onDrawingEditRequested?.invoke(strokeStyle)
    }

    fun switchToEraserMode() {
        _menuBarState.update { currentState ->
            val newState = currentState.copy(
                currentMenuType = MenuBar.BRUSH,
                isPassiveEraserActive = true,
                isEraserActive = false,
                isBrushActive = true,
                isRulerActive = false
            )
            newState
        }
        // 执行绘图模式下的通用操作
        performCommonDrawingModeActions()
    }

    /**
     * 橡皮擦按钮点击
     */
    fun onEraserClick() {
        // 重置按钮状态
        resetAllButtonStates()
        
        _menuBarState.update { currentState ->
            val newState = currentState.copy(
                currentMenuType = MenuBar.BRUSH,
                isEraserActive = true,
                isRulerActive = false // 确保直尺被禁用
            )
            newState
        }
        // 更新当前菜单类型状态
        updateCurrentMenuType(MenuBar.BRUSH)
        
        // 执行绘图模式下的通用操作
        performCommonDrawingModeActions()
    }

    /**
     * 套索工具点击
     */
    fun onLassoClick() {
        // 重置按钮状态
        resetAllButtonStates()
        
        _menuBarState.update { currentState ->
            currentState.copy(
                currentMenuType = MenuBar.BRUSH,
                isLassoActive = true,
                isRulerActive = false // 确保直尺被禁用
            )
        }
        
        // 更新当前菜单类型状态
        updateCurrentMenuType(MenuBar.BRUSH)
        
        // 执行绘图模式下的通用操作
        performCommonDrawingModeActions()

    }


    /**
     * 一笔成型工具点击
     */
    fun onShapeRecognizeClick(enable: Boolean) {
        if (enable) {
            // 重置按钮状态
            resetAllButtonStates()

            _menuBarState.update { currentState ->
                currentState.copy(
                    currentMenuType = MenuBar.BRUSH,
                    isBrushActive = true,
                    isShapeRecognize = true,
                    isEraserActive = false,
                    isRulerActive = false // 确保直尺被禁用
                )
            }

            // 执行绘图模式下的通用操作
            performCommonDrawingModeActions()
        } else {
            _menuBarState.update { currentState ->
                currentState.copy(
                    currentMenuType = MenuBar.BRUSH,
                    isShapeRecognize = false,
                )
            }
        }
    }

    /**
     * 尺子工具点击
     */
    fun onRulerClick() {
        // 检查当前直尺状态
        val currentRulerActive = _menuBarState.value.isRulerActive
        // 如果直尺已经激活，则关闭它
        if (currentRulerActive) {
            _menuBarState.update { currentState ->
                currentState.copy(isRulerActive = false)
            }
        } else {
            // 如果直尺未激活，则激活它
            // 不重置所有按钮状态，让直尺与笔刷共存
            
            // 如果当前不是绘图模式或笔刷未激活，则切换到绘图模式并激活笔刷
            if (_menuBarState.value.currentMenuType != MenuBar.BRUSH || !_menuBarState.value.isBrushActive) {
                resetDrawingButtonStates()
                
                _menuBarState.update { currentState ->
                    currentState.copy(
                        currentMenuType = MenuBar.BRUSH,
                        isBrushActive = true,
                        isRulerActive = true
                    )
                }
                
                // 更新当前菜单类型状态
                updateCurrentMenuType(MenuBar.BRUSH)
                
                // 执行绘图模式下的通用操作
                performCommonDrawingModeActions()
            } else {
                // 如果已经在绘图模式，仅激活直尺
                _menuBarState.update { currentState ->
                    currentState.copy(isRulerActive = true)
                }
            }
        }
        
        showToast("尺子功能待接入...")
    }

    /**
     * 手写转文本工具点击
     */
    fun onHandwritingToTextClick() {
        // 重置按钮状态
        resetAllButtonStates()
        
        _menuBarState.update { currentState ->
            currentState.copy(
                currentMenuType = MenuBar.BRUSH,
                isHandwritingToTextActive = true,
                isRulerActive = false // 确保直尺被禁用
            )
        }
        
        // 更新当前菜单类型状态
        updateCurrentMenuType(MenuBar.BRUSH)
        
        // 执行绘图模式下的通用操作
        performCommonDrawingModeActions()
        
        showToast("手写转文本功能待接入...")
    }

    /**
     * 撤销操作
     */
    fun onUndoClick() {
        val isDrawingMode = _menuBarState.value.currentMenuType == MenuBar.BRUSH
        
        if (isDrawingMode) {
            if (drawingHistoryPosition > 0) {
                drawingHistoryPosition--
                onUndoRequested?.invoke()
            }
        } else {
            if (textHistoryPosition > 0) {
                textHistoryPosition--
                onUndoRequested?.invoke()
            }
        }
        
        updateUndoRedoState()
    }

    /**
     * 重做操作
     */
    fun onRedoClick() {
        val isDrawingMode = _menuBarState.value.currentMenuType == MenuBar.BRUSH
        
        if (isDrawingMode) {
            if (drawingHistoryPosition < drawingHistory.size - 1) {
                drawingHistoryPosition++
                onRedoRequested?.invoke()
            }
        } else {
            if (textHistoryPosition < textEditHistory.size - 1) {
                textHistoryPosition++
                onRedoRequested?.invoke()
            }
        }
        
        updateUndoRedoState()
    }

    /**
     * AI助手点击
     */
    fun onAiClick() {
        // 使用全重置方法，确保所有绘图工具状态都被清除，包括直尺
        resetAllButtonStates()
        
        _menuBarState.update { currentState ->
            currentState.copy(
                currentMenuType = MenuBar.AI,
                isAiActive = true,
                isRulerActive = false  // 确保直尺被禁用
            )
        }
        
        // 更新当前菜单类型状态
        updateCurrentMenuType(MenuBar.AI)
        
        showToast("AI助手功能待接入...")
    }

    /**
     * 美化工具点击
     */
    fun onBeautifyClick() {
        // 重置按钮状态
        resetAllButtonStates()
        
        _menuBarState.update { currentState ->
            currentState.copy(
                currentMenuType = MenuBar.BRUSH,
                isBeautifyActive = true,
                isRulerActive = false // 确保直尺被禁用
            )
        }
        
        // 更新当前菜单类型状态
        updateCurrentMenuType(MenuBar.BRUSH)
        
        // 执行绘图模式下的通用操作
        performCommonDrawingModeActions()
        
        showToast("美化功能待接入...")
    }

    /**
     * 平移模式点击
     */
    fun onPanOnlyClick() {
        // 切换平移模式状态，但不重置其他状态
        // 因为平移模式是与其他功能可以共存的
        _menuBarState.update { currentState ->
            currentState.copy(isPanOnly = !currentState.isPanOnly)
        }
    }

    private fun showToast(message: String) {
        ComposableToast.show(GlobalContext.instance.applicationContext, message)
    }
}

/**
 * 菜单栏UI状态数据类
 */
data class MenuBarUiState(
    // 当前菜单类型
    val currentMenuType: MenuBar = MenuBar.KEYBOARD,
    val currentBrushType: BrushType = BrushType.FOUNTAIN_PEN,

    // 按钮状态
    val isKeyboardActive: Boolean = true,
    val isBrushActive: Boolean = false,
    //标记按下手写笔按键时切换橡皮擦模式
    val isPassiveEraserActive: Boolean = false,
    val isEraserActive: Boolean = false,
    val isLassoActive: Boolean = false,
    val isRulerActive: Boolean = false,
    val isHandwritingToTextActive: Boolean = false,
    val isAiActive: Boolean = false,
    val isBeautifyActive: Boolean = false,
    val isPanOnly: Boolean = false,

    // 撤销/重做
    val canUndo: Boolean = false,
    val canRedo: Boolean = false,

    val isShapeRecognize: Boolean = false,


    )


