package com.tcl.ai.note.handwritingtext.sunia

import android.content.Context
import android.graphics.RectF
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.lifecycle.viewModelScope
import com.sunia.penengine.sdk.operate.canvas.ScaleInfo
import com.sunia.penengine.sdk.operate.edit.StepType
import com.sunia.singlepage.sdk.param.PointType
import com.tcl.ai.note.handwritingtext.controller.NoteDataSaveController
import com.tcl.ai.note.handwritingtext.repo.HandWritingThumbnailRepo
import com.tcl.ai.note.handwritingtext.repo.NoteRepository2
import com.tcl.ai.note.handwritingtext.repo.idToEntPath
import com.tcl.ai.note.handwritingtext.sunia.history.DrawEditOperation
import com.tcl.ai.note.handwritingtext.sunia.view.SuniaDrawView
import com.tcl.ai.note.handwritingtext.vm.draw.LassoRotateEvent
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.suspend
import com.tcl.ai.note.utils.tryRequestFocus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SuniaDrawViewController(
    private val context: Context,
    private val suniaDrawViewModel: SuniaDrawViewModel
) {
    private val view = SuniaDrawView(context)
    private var onSetVisibleSizeFinishCallback: () -> Unit = {}
    private var onCanvasHandleFinishCallback: () -> Unit = {}
    private var onLastDrawRectCallback: (RectF) -> Unit = {}
    private var onSizeChangeStartCallback: () -> Unit = {}
    private var onTextModeSwitchDrawModeCallback: (Boolean) -> Unit = {}

    // 仅修改时触发缩略图保存
    private var needToSaveThumbnail = false


    private val viewListener = object : SuniaDrawView.Listener {
        override fun onScale(scaleInfo: ScaleInfo) {
            // 让viewmodel保存缩放信息，并通知富文本缩放
            // ScaleInfo的地址都是相同的，安格斯只是修改的这个对象的内容，然后回调
//            suniaDrawViewModel.changeScaleInfo(scaleInfo)
        }

        override fun onDataLoaded(
            drawStrokeCount: Int,
            drawStrokeRect: RectF,
            isSuccess: Boolean
        ) {
            suniaDrawViewModel.drawStokeVisibleRect = drawStrokeRect
            // 数据加载完成后更新undo/redo状态
            updateUndoRedoState()
            // 初始化图片数量
            getImageCount()
        }

        override fun onStepChanged(
            stepType: Int,
            stepId: Int,
            stepName: String?,
            canUndo: Boolean,
            canRedo: Boolean,
            drawStrokeCount: Int,
            drawStrokeRect: RectF
        ) {
            suniaDrawViewModel.drawStokeVisibleRect = drawStrokeRect
            needToSaveThumbnail = true
            suniaDrawViewModel.saveDraw()
            /*suniaDrawViewModel.viewModelScope.launchIO {
                suniaDrawViewModel.isContentEmpty(view.isContentEmpty())
            }*/
            if (suniaDrawViewModel.autoSave.value) {
                suniaDrawViewModel.autoSaveJournalContent()
            }
            // 直接使用SDK回调提供的canUndo和canRedo状态
            suniaDrawViewModel.updateUndoRedoState(canUndo, canRedo)
            if (stepType == StepType.CRAETE.value) {
                suniaDrawViewModel.run {
                    createOperationWithoutUndoRedo(
                        DrawEditOperation(
                            stepName ?: "",
                            drawStrokeCount,
                            drawStrokeRect
                        )
                    )
                }
            }
        }

        override fun onSetVisibleSizeFinish() {
            onSetVisibleSizeFinishCallback.invoke()
        }

        override fun onCanvasHandleFinish() {
            onCanvasHandleFinishCallback.invoke()
            // 每次缩放结束都要更新橡皮擦，否则橡皮擦会跟随缩放大小变化
            suniaDrawViewModel.updateEraseSizeWhenScaleChanged()
        }

        override fun onLastDrawDataRect(lastDrawRect: RectF) {
            onLastDrawRectCallback.invoke(lastDrawRect)
        }

        override fun onTouchPositionUpdate(position: androidx.compose.ui.geometry.Offset?) {
            suniaDrawViewModel.updateCurrentTouchPosition(position)
        }

        override fun onSizeChangeStart() {
            onSizeChangeStartCallback.invoke()
        }

        override fun onInitEngineFinish() {
            Logger.d(TAG, "onInitEngineFinish")
            suniaDrawViewModel.setEngineInitComplete(true)
        }

        override fun rendToScreen(rectF: RectF, state: Int) {

        }

        override fun onSaveCompleted(pageIndex: Int) {
            if (suniaDrawViewModel.haveEdit) {
                suniaDrawViewModel.forceUpdatePageIndex(pageIndex)
                suniaDrawViewModel.haveEdit = false
            }
        }
    }

    private val editMenuListener = object : SuniaDrawView.OnShowEditMenuListener {
        override fun onShowImageEditMenu(x: Float, y: Float) {
            suniaDrawViewModel.updateEditState(true, x, y)
        }

        override fun onHintEditMenu() {
            suniaDrawViewModel.updateEditState(false)
        }

        override fun onLassoRotateAngle(rotate: LassoRotateEvent?) {
            suniaDrawViewModel.updateRotateState(rotate)
        }
    }

    fun onSetVisibleSizeFinish(action: () -> Unit) {
        onSetVisibleSizeFinishCallback = action
    }

    /**
     * sunia完成绘制回调
     */
    fun onCanvasHandleFinish(action: () -> Unit) {
        onCanvasHandleFinishCallback = action
    }

    /**
     * sunia触发大小变更回调
     */
    fun onSizeChangeStart(action: () -> Unit) {
        onSizeChangeStartCallback = action
    }

    fun onLastDrawRect(action: (RectF) -> Unit) {
        onLastDrawRectCallback = action
    }

    fun onTextModeSwitchDrawMode(action: (Boolean) -> Unit) {
        onTextModeSwitchDrawModeCallback = action
    }

    fun attachViewGroup(viewGroup: ViewGroup) {
        viewGroup.addView(view)
        // view.setOnHoverListener { _, _ -> return@setOnHoverListener true }
    }

    fun dispatchTouchEvent(event: MotionEvent) {
        view.dispatchTouchEvent(event)
    }

    /**
     * 1. 把作用域切换到suniaDrawViewModel
     * 2. 实现ViewModel对应的方法
     */
    private fun implViewModelFunc() = with(suniaDrawViewModel) {
        // 实现suniaDrawViewModel的loadDrawStroke方法
        view.implLoadDrawStrokeFunc = suspend() { noteId ->
            view.loadData(noteId.idToEntPath())
        }

        // 实现suniaDrawViewModel的saveDrawStroke方法
        view.implSaveDrawStrokeFunc = { noteId, onDataSavedFunc ->
            NoteDataSaveController.saveDrawStroke {
                val isSuccess = view.saveDate(noteId.idToEntPath())
                onDataSavedFunc(isSuccess)
            }
        }

        view.implSaveThumbnailFunc = { noteId, _ ->
            if (!needToSaveThumbnail) {
                Logger.d(TAG, "needToSaveThumbnail false, return")
            } else {
                NoteDataSaveController.saveThumbnail {
                    view.saveThumbnailBlockWithDark(noteId)
                    // 更新缩略图后写入note数据库，目前因额外保存黑夜模式，该功能无作用
                    HandWritingThumbnailRepo.getBitmapPath(noteId)?.let { path ->
                        NoteRepository2.updateThumbnail(noteId, path)
                    }
                }
            }
        }

        // 实现suniaDrawViewModel的setViewVisibility方法,
        view.implSetViewVisibilityFunc = view::setVisibility

        // 实现suniaDrawViewModel的changeScale方法,
        view.implChangeScaleInfoFunc = { view.changeScale(it) }

        // 实现suniaDrawViewModel的changeScaleRelative方法,
        view.implChangeScaleRelativeFunc = { view.changeScaleRelative(it) }

        // 实现suniaDrawViewModel的changeScaleRelativeEnd方法,
        view.implChangeScaleEndFunc = { view.changeScaleRelativeEnd(it) }

        // 实现suniaDrawViewModel的clearAllDrawStroke方法,
        view.implClearAllDrawStrokeFunc = { view.clearAllDrawStroke() }

        // 实现suniaDrawViewModel的undo方法,
        view.implUndoFunc = {
            view.undo()
            // 撤销后立即更新undo/redo状态
            updateUndoRedoState()
        }

        // 实现suniaDrawViewModel的redo方法,
        view.implRedoFunc = {
            view.redo()
            // 重做后立即更新undo/redo状态
            updateUndoRedoState()
        }

        // 实现获取画板有效内容范围
        view.implGetScaledContentRangeFunc = {
            view.getScaledContentRange()
        }

        view.implToBitmapFunc = { bitmap ->
            view.toBitmap(bitmap)
        }


        view.saveJournalFunc = { journalId, pageIndex ->
            view.saveJournalContent(journalId, pageIndex, null)
        }

        view.implClearAllFunc = { view.clear() }

        view.implClearStepFunc = { view.clearStep()}

        view.handleContentInfoFunc = { journalContentInfo, block ->
            view.handleContentInfo(journalContentInfo = journalContentInfo, onComplete = block)
        }
        view.implReplaceTextFunc = { textList, journalContentInfo ->
            view.replaceText(textList, journalContentInfo)
        }
        view.implAddTextFunc = { text ->
            view.addText(text)
        }
        view.clearTextFunc = {
            view.clearText()
        }

        view.implDeleteEditSelectFunc = {
            view.deleteSelect()
        }
        view.implFinishSelectFunc = {
            view.finishEditSelect()
        }
        Logger.d(TAG, "implViewModelFunc end")
    }

    init {
        // 实现viewmodel里面的方法
        implViewModelFunc()
        // 监听view数据
        view.addListener(viewListener)
        view.setEditMenuListener(editMenuListener)

        suniaDrawViewModel.viewModelScope.launchIO {
            suniaDrawViewModel.enableFingerDrawingState.collect {
                view.enableFingerDrawing = it
            }
        }

        suniaDrawViewModel.viewModelScope.launchIO {
            suniaDrawViewModel.penPropState.collect{
                view.changePenProp(it)
            }
        }

        // 监听橡皮擦属性变化
        suniaDrawViewModel.viewModelScope.launch(Dispatchers.Main.immediate) {
            suniaDrawViewModel.deletePropState.collect { deleteProp ->
                Logger.d(TAG,"collect, monitor eraser attribute changes, deleteProp:$deleteProp")
                view.changeErase(deleteProp)
            }
        }

        suniaDrawViewModel.viewModelScope.launchIO {
            suniaDrawViewModel.insertBitmapSharedFlow.collect { event ->
                // 延迟400ms，选择图片回到页面后， SunialDrawBoard# onResume 会延时执行，导致图片无法进入编辑模式
                delay(400)
                // 插入图片，切换到手绘模式
                onTextModeSwitchDrawModeCallback.invoke(true)
                view.insertBitmap(
                    path = event.path,
                    rectF = event.rectF,
                    isSelect = true
                )
                getImageCount()
            }
        }

        suniaDrawViewModel.viewModelScope.launchIO {
            suniaDrawViewModel.onClickPointState.collect { event ->
                val hasImage = view.clickPointByType(
                    event,
                    PointType.POINT_TYPE_BITMAP
                )
                onTextModeSwitchDrawModeCallback.invoke(hasImage)
            }
        }

        suniaDrawViewModel.viewModelScope.launchIO {
            suniaDrawViewModel.shapeRecognizeState.collect { enable ->
                if (enable) {
                    changeEditable(true)
                }
                view.setShapeRecognizeEnable(enable)
            }
        }

        suniaDrawViewModel.viewModelScope.launchIO {
            view.textRectFListState.collect {
                suniaDrawViewModel.setTextRectFList(it)
            }
        }

        suniaDrawViewModel.viewModelScope.launchIO {
            suniaDrawViewModel.lassoState.collect{
                view.setLassoEnable(it)
            }
        }
    }

    /**
     * 更新undo/redo状态
     */
    private fun updateUndoRedoState() = suniaDrawViewModel.viewModelScope.launch {
        val canUndo = view.canUndo()
        val canRedo = view.canRedo()
        suniaDrawViewModel.updateUndoRedoState(canUndo, canRedo)
    }

    fun changeEditable(editable: Boolean) {
        if (editable) {
            view.isFocusable = true
            view.isFocusableInTouchMode = true
            view.tryRequestFocus()
        } else {
            view.clearFocus()
            view.isFocusable = false
            view.isFocusableInTouchMode = false
        }
        view.changeEditable(editable)
    }

    fun changeDarkTheme(isDark: Boolean) {
        view.changeDarkMode(isDark)
        suniaDrawViewModel.isDarkTheme = isDark
    }

    fun getImageCount() = suniaDrawViewModel.viewModelScope.launch {
        val imageCount = view.getImageCount()
        suniaDrawViewModel.setImageCount(imageCount)
    }

    companion object {
        private const val TAG = "SuniaDrawViewController"
    }
}

class DebouncedSaver(
    private val scope: CoroutineScope,
    private val delayMillis: Long = 100L,
    private val saveAction: () -> Unit
) {
    private var saveJob: Job? = null

    fun onEvent() {
        // 取消前一次未完成的保存任务
        saveJob?.cancel()
        // 启动新的延迟保存任务
        saveJob = scope.launch {
            delay(delayMillis)
            saveAction()
        }
    }
}