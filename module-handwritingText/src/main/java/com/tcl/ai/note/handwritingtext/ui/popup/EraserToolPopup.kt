package com.tcl.ai.note.handwritingtext.ui.popup

import android.annotation.SuppressLint
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup.EraserToolBar2
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx

/** 橡皮擦弹窗的标准宽度 */
private val ERASER_POPUP_WIDTH = 328.dp

/**
 * 通用的橡皮擦工具栏内容组件
 * 提取了 EraserToolBar2 的通用配置逻辑
 */
@Composable
private fun EraserToolContent(
    modifier: Modifier = Modifier,
    eraserViewModel: EraserViewModel,
    suniaDrawViewModel: SuniaDrawViewModel,
    closePopup: () -> Unit,
    onSwitchToBrush: () -> Unit
) {
    
    EraserToolBar2(
        modifier = modifier,
        eraserViewModel = eraserViewModel,
        onClose = {
            closePopup()
        },
        onClearAll = {
            // 清除所有绘图内容
            suniaDrawViewModel.clearAllDrawStroke()
            // 切换到BRUSH模式
            onSwitchToBrush()
            closePopup()
        },
        onEraserSizeChange = { size, eraserMode ->
            // 切换擦除模式与擦除大小
            suniaDrawViewModel.switchEraser(eraserMode, size)
        }
    )
}

/**
 * 平板端橡皮擦工具弹窗
 * 从菜单栏图标位置弹出，显示在图标下方
 */
@SuppressLint("ConfigurationScreenWidthHeight")
@Composable
fun EraserToolPopup(
    menuBarItem: MenuBarItem,
    eraserViewModel: EraserViewModel = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    onDismissRequest: () -> Unit,
    onSwitchToBrush: () -> Unit = {}
) {
    val density = LocalDensity.current
    val space = 23.dp
    val dimens = getGlobalDimens()
    val position: Offset = menuBarItem.position
    val screenWidthDp = LocalConfiguration.current.screenWidthDp
    val layoutDirection = LocalLayoutDirection.current

    val xOffset = with(density) {
        if (layoutDirection == androidx.compose.ui.unit.LayoutDirection.Rtl) {
            (screenWidthDp.dp.toPx() - position.x - dimens.btnSize.toPx / 2 - ERASER_POPUP_WIDTH.toPx() / 2).toInt()
        } else {
            (position.x + dimens.btnSize.toPx / 2 - ERASER_POPUP_WIDTH.toPx() / 2).toInt()
        }
    }
    val yOffset = (space.toPx + position.y).toInt()

    BounceScalePopup(
        onDismissRequest = onDismissRequest,
        offset = IntOffset(xOffset, yOffset),
    ) { closePopup ->
        EraserToolContent(
            eraserViewModel = eraserViewModel,
            suniaDrawViewModel = suniaDrawViewModel,
            closePopup = closePopup,
            onSwitchToBrush = onSwitchToBrush
        )
    }
}


/**
 * 手机端橡皮擦工具弹窗
 * 从底部滑出，居中显示
 * 
 * @param areaHeight 可用区域高度
 */
@SuppressLint("ConfigurationScreenWidthHeight")
@Composable
fun PhoneEraserToolPopup(
    eraserViewModel: EraserViewModel = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    areaHeight: Int,
    onDismissRequest: () -> Unit,
    onSwitchToBrush: () -> Unit = {}
) {
    val space = 8.dp
    val density = LocalDensity.current
    val screenWidthDp = LocalConfiguration.current.screenWidthDp

    val show = eraserViewModel.selectedMode == 0
    val xOffset = with(density) {
        (screenWidthDp.dp.toPx() - ERASER_POPUP_WIDTH.toPx()) / 2
    }

    val animatedHeightOffset by animateDpAsState(
        targetValue = if (show) 295.dp else 247.dp,
        animationSpec = tween(300,400),
        label = ""
    )

    val yOffset = areaHeight - animatedHeightOffset.toPx - with(density) { space.toPx() }



    SlideFromBottomPopup(
        onDismissRequest = onDismissRequest,
        offset = IntOffset(xOffset.toInt(), yOffset.toInt())
    ) { closePopup ->

        EraserToolContent(
            modifier = Modifier.height(if (show) 295.dp else animatedHeightOffset),
            eraserViewModel = eraserViewModel,
            suniaDrawViewModel = suniaDrawViewModel,
            closePopup = closePopup,
            onSwitchToBrush = onSwitchToBrush
        )


    }
}

