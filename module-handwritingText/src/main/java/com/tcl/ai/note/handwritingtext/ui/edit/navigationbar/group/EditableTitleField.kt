package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.text
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.zIndex
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.CursorColor
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.ComposableToast
import kotlinx.coroutines.delay

@SuppressLint("DesignSystem")
@Composable
fun EditableTitleField(
    title: String,
    onTitleChanged: (String) -> Unit,
    isEditing: Boolean,
    onEditingChanged: (Boolean) -> Unit,
    focusRequester: FocusRequester,
    keyboardController: SoftwareKeyboardController?,
    modifier: Modifier = Modifier,
    onUserStartedEditing: (() -> Unit)? = null
) {
    val context = LocalContext.current
    val tip = stringResource(R.string.exceeds_maximum_characters)

    var isFocused by remember { mutableStateOf(false) }
    val hintText = stringResource(R.string.title)
    
    // 内部维护一个实际显示的文本状态
    var displayText by remember(title) { 
        mutableStateOf(if (title == hintText) "" else title)
    }
    
    // 判断是否为初始hint状态
    var isHintState by remember(title) { mutableStateOf(title.isEmpty() || title == hintText) }
    
    // 记录用户是否已经开始输入过（用于标记真正编辑）
    var hasUserTyped by remember { mutableStateOf(false) }
    
    // 当外部title发生变化时，同步更新内部状态
    LaunchedEffect(title) {
        if (!isFocused) { // 只有在未获得焦点时才同步外部值，避免覆盖用户正在编辑的内容
            displayText = if (title == hintText) "" else title
            isHintState = title.isEmpty() || title == hintText
            // 重置用户输入状态，这通常意味着切换到了新的笔记或重新加载数据
            hasUserTyped = false
        }
    }
    
    // 当进入编辑状态时请求焦点
    LaunchedEffect(isEditing) {
        if (isEditing && !isFocused) {
            // 延迟请求焦点，确保UI完全初始化
            delay(100)
            try {
                focusRequester.requestFocus()
            } catch (e: Exception) {
                // 忽略焦点请求失败
            }
        }
    }
    
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.CenterStart
    ) {
        // 在空文本时显示hint，除非正在输入中（isFocused && !isHintState）
        if (displayText.isEmpty() && (!isFocused || isHintState)) {
            Text(
                text = hintText,
                style = TextStyle(
                    color = TclTheme.colorScheme.textHint,
                    fontSize = TclTheme.textSizes.titleSmall,
                    fontWeight = FontWeight.Medium
                ),
                maxLines = 1,
                modifier = Modifier
                    .zIndex(0f)
                    .clickable {
                        // 点击提示文本时进入编辑状态
                        onEditingChanged(true)
                        focusRequester.requestFocus()
                    }
            )
        }
        
        BasicTextField(
            value = displayText,
            onValueChange = { newValue ->
                if (newValue.length <= 50) {
                    displayText = newValue
                    isHintState = newValue.isEmpty()
                    
                    // 当用户第一次开始输入时，标记为已编辑
                    if (!hasUserTyped) {
                        hasUserTyped = true
                        onUserStartedEditing?.invoke()
                    }
                    
                    onTitleChanged(newValue)
                    // 当用户开始输入时，确保进入编辑状态
                    if (!isEditing) {
                        onEditingChanged(true)
                    }
                } else {
                    ComposableToast.show(context, tip)
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .focusRequester(focusRequester)
                .onFocusChanged { focusState ->
                    isFocused = focusState.isFocused
                    if (focusState.isFocused) {
                        if (isHintState) {
                            displayText = ""
                        }
                        // 获得焦点时进入编辑状态
                        if (!isEditing) {
                            onEditingChanged(true)
                        }
                    } else {
                        // 失去焦点时退出编辑状态
                        if (isEditing) {
                            onEditingChanged(false)
                        }
                    }
                }
                .semantics {
                    // 设置输入框的描述，说明这是什么类型的输入框
                    this.contentDescription = context.getString(R.string.title_input_field)
                    // 设置实际的文本内容，确保无障碍服务读出用户输入的内容
                    this.text = AnnotatedString(
                        displayText.ifEmpty {
                            // 当文本为空时，读出提示文本
                            hintText
                        }
                    )
                }
                .zIndex(1f),
            textStyle = TextStyle(
                color = TclTheme.colorScheme.tctStanderTextPrimary,
                fontSize = TclTheme.textSizes.bodyLarge
            ),
            cursorBrush = SolidColor(CursorColor),
            enabled = true,
            readOnly = false,
            singleLine = true,
            maxLines = 1,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = {
                    keyboardController?.hide()
                    onEditingChanged(false)
                }
            )
        )
    }
    
    BackHandler(enabled = isEditing) {
        keyboardController?.hide()
        onEditingChanged(false)
    }
}