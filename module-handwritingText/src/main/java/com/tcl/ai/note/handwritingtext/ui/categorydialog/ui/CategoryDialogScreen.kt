package com.tcl.ai.note.handwritingtext.ui.categorydialog.ui

import android.annotation.SuppressLint
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.LifecycleResumeEffect
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogType
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogViewModel
import com.tcl.ai.note.handwritingtext.ui.categorydialog.DialogNote
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils


/**
 * 主要的弹窗管理组件 - 内部封装ViewModel能力
 *
 * 使用方法:
 * 1. 创建一个dialogController = rememberCategoryDialogController()
 * 2. 在需要显示弹窗的地方调用dialogController的方法
 * 3. 将dialogController.state.value传递给CategoryDialogManager
 *
 * Toast提示
 * 首页：
 * 1.移动
 * 1.1 单个：已将 所选1条笔记移动至 ...("..."为分类名，超过两行后...)
 * 1.2 多个：已将 所选n条笔记移动至 ...("..."为分类名，超过两行后...)
 * 2.重命名：已重命名为 ...（“...”为分类名，超过两行后...）
 * 3.新建：无需Toast
 *
 * 编辑页：
 * 1.移动：已将 所选1条笔记移动至 ...("..."为分类名，超过两行后...)
 * 2.新建后移动：已将 所选1条笔记移动至 ...("..."为分类名，超过两行后...)
 */
@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun CategoryDialogScreen(
    categoryViewModel: CategoryDialogViewModel = hiltViewModel(),
    screenKey: String, // 添加一个唯一标识符参数
    onSuccess: ((String) -> Unit)? = null // 成功回调，可以传递成功消息
) {
    // 在组件进入时重置状态 解决首页和编辑页 重复弹出移动分类弹窗问题
    LifecycleResumeEffect(screenKey) {
        // 当组件进入时，确保弹窗状态是关闭的
        categoryViewModel.resetDialogState()
        categoryViewModel.observeCategoryListAndDialogState()
        onPauseOrDispose {
            categoryViewModel.resetDialogState()
        }
    }
    // 获取分类列表
    val dialogController = categoryViewModel.categoryDialogController
    val dialogState by categoryViewModel.categoryDialogState.collectAsState()
    val dialogCategory = categoryViewModel.categoryDialogState.value.editingDialogCategory

    val notesToMove = dialogState.notesToMove
    val noteCategories = dialogState.noteCategories
    val selectedCategoryId = dialogState.selectedCategoryId
    val isPreviewMode = dialogState.isPreviewMode

    //这里的文案提示需要区分，新建分类是公用的，如果是从选择分类弹窗中触发的新建，新建完 移动note到当前新建，此时Toast提示“移动至分类成功”
    //如果是侧边栏新建，Toast提示“新建分类成功”
    val toastFail = stringResource(R.string.category_add_fail)
    val context = LocalContext.current

    Logger.i(TAG, "dialogState:$dialogState")

    if (dialogState.isVisible) {
        when (dialogState.type) {
            CategoryDialogType.NEW_CATEGORY -> {
                NewCategoryDialog(
                    dialogCategory = null,
                    dialogState = dialogState,
                    isNewCategory = true,
                    onConfirm = { name, colorIndex ->
                        // Add new category
                        //限制最多创建分类个数为50个(不管是移动新建，还是侧边栏新建)
                        Logger.i(
                            TAG,
                            "noteCategories.size:${noteCategories.size}, notesToMove.size:${dialogState.notesToMove.size}"
                        )
                        if (noteCategories.size >= 50) {
                            ToastUtils.makeWithCancel(toastFail, Toast.LENGTH_SHORT)
                            dialogController.dismiss()
                            return@NewCategoryDialog
                        }
                        val category = NoteCategory(
                            name = name,
                            colorIndex = colorIndex,
                            createTime = System.currentTimeMillis(),
                            modifyTime = System.currentTimeMillis()
                        )
                        val noteIds = dialogState.notesToMove.map { it.id }
                        categoryViewModel.addCategory(
                            category,
                            noteIds,
                            dialogState.isMoveToNewCategory
                        )
                        if (dialogState.isMoveToNewCategory) {
                            val toastSuccess = context.getString(
                                R.string.category_moveto_success_new,
                                dialogState.notesToMove.size.toString(),
                                name
                            )
                            ToastUtils.makeWithCancel(toastSuccess, Toast.LENGTH_SHORT)
                        }

                        dialogController.dismiss()
                    },
                    onDismiss = { dialogController.dismiss() }
                )
            }

            CategoryDialogType.EDIT_CATEGORY -> {
                NewCategoryDialog(
                    dialogCategory = dialogCategory,
                    dialogState = dialogState,
                    isNewCategory = false,
                    onConfirm = { name, index ->
//                            Rename category
                        Logger.i(
                            TAG,
                            "Rename, original category, dialogCategory:$dialogCategory, name:$name, index:$index"
                        )
                        dialogCategory?.categoryId?.let { categoryId ->
                            val newCategory = NoteCategory(
                                categoryId = categoryId,
                                name = name,
                                colorIndex = index,
                            )
                            newCategory.modifyTime = System.currentTimeMillis()
                            newCategory.name = name
                            newCategory.colorIndex = index
                            newCategory.isRename = dialogCategory.name != name
                            categoryViewModel.renameCategory(newCategory)
                            val toastMsg = context.getString(R.string.category_rename, name)
                            ToastUtils.makeWithCancel(toastMsg, Toast.LENGTH_SHORT)
                        }
                        dialogController.dismiss()
                    },
                    onDismiss = {
                        dialogController.dismiss()
                        categoryViewModel.onCategoryDialogDismiss()
                    }
                )
            }

            CategoryDialogType.MOVE_NOTE_TO_CATEGORY -> {
                Logger.i(
                    TAG,
                    "Move，selectedCategoryId:$selectedCategoryId, noteCategories:$noteCategories"
                )
                //如果selectedCategoryId是空，说明选中的是全部便签
                val result = inSameCategory(notesToMove)
                val inSameCategory = result.first
                MoveNoteToCategoryDialog(
                    dialogController = dialogController,
                    notesToMove = dialogState.notesToMove,
                    inSameCategory = inSameCategory,
                    categories = noteCategories,
                    categoryId = result.second,
                    onMoveToCategory = { categoryId ->
                        //移动之后，取消选中模式，并自动切换到选中的分类
                        if (dialogState.notesToMove.isNotEmpty()) {
                            val noteIds = dialogState.notesToMove.map { it.id }
                            categoryViewModel.updateNotesCategoryId(
                                noteIds,
                                categoryId,
                                isPreviewMode
                            )
                        }

                        dialogController.dismiss()
                    },
                    onDismiss = {
                        dialogController.dismiss()
                        categoryViewModel.onCategoryDialogDismiss()
                    }
                )
            }
        }
    }
}


/**
 * 选中的Note是否属于同一个分类
 */
fun inSameCategory(noteCategories: List<DialogNote>): Pair<Boolean, Long> {
    // 获取第一个元素的categoryId作为基准
    val firstCategoryId = noteCategories.first().categoryId

    // 处理空列表和单元素列表的情况
    if (noteCategories.size <= 1) return Pair(true, firstCategoryId)

    // 检查后续所有元素的categoryId是否与基准一致
    return Pair(noteCategories.all { it.categoryId == firstCategoryId }, firstCategoryId)
}

internal const val TAG = "CategoryDialogScreenManager"