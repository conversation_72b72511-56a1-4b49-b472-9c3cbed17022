package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.DisposableEffect
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group.EditableTitle
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group.NavigationActionArea
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group.NavigationBackButton
import com.tcl.ai.note.handwritingtext.ui.richtext.state.EditableTitleNavigationBarState
import com.tcl.ai.note.handwritingtext.vm.TitleEditViewModel
import com.tcl.ai.note.theme.TclTheme
import kotlinx.coroutines.launch


/**
 * 带可编辑标题的导航栏（暂只适配平板）
 */
@SuppressLint("DesignSystem")
@Composable
fun EditableTitleNavigationBar(
    modifier: Modifier = Modifier,
    title: String = "",
    onTitleChanged: (String) -> Unit = {},
    onTitleEditingStateChanged: (Boolean) -> Unit = {},
    onUserStartedEditing: (() -> Unit)? = null,
    navigationBarState: EditableTitleNavigationBarState? = null,
    onNavigationBarStateChanged: ((EditableTitleNavigationBarState) -> Unit)? = null
) {
    val defaultTitle = stringResource(R.string.title)
    var state by remember {
        mutableStateOf(
            navigationBarState ?: EditableTitleNavigationBarState(
                title = title.ifEmpty { defaultTitle },
                hasUserEditedTitle = title.isNotEmpty() && title != defaultTitle
            )
        )
    }
    
    // 如果提供了外部state，使用外部state
    if (navigationBarState != null) {
        state = navigationBarState
    }

    // 防重复点击状态
    var lastBackClickTime by remember { mutableLongStateOf(0L) }
    var isNavigating by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current

    // 当外部title发生变化时，同步更新state
    LaunchedEffect(title) {
        if (!state.isEditing) { // 只有在非编辑状态下才同步外部值
            val newState = state.copy(
                title = title.ifEmpty { defaultTitle },
                hasUserEditedTitle = title.isNotEmpty() && title != defaultTitle
            )
            state = newState
            onNavigationBarStateChanged?.invoke(newState)
        }
    }

    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    //val keyboardController = LocalSoftwareKeyboardController.current
    val density = LocalDensity.current

    val titleEditViewModel: TitleEditViewModel = hiltViewModel()
    val forceExitEditing by titleEditViewModel.forceExitEditing
    val lifecycleOwner = LocalLifecycleOwner.current

    // 统一处理标题保存
    val saveTitle = {
        handleTitleSave(
            state = state,
            defaultTitle = defaultTitle,
            onTitleChanged = onTitleChanged,
            updateState = { newState ->
                state = newState
                onNavigationBarStateChanged?.invoke(newState)
            },
            focusManager = focusManager,
            keyboardController = null
        )
    }

    // 点击返回处理逻辑
    val handleBackClick = {
        if (state.isEditing) {
            // 如果正在编辑，直接退出编辑状态，不需要防重
            saveTitle()
        } else {
            // 如果不在编辑状态，进行导航返回，需要防重
            val currentTime = System.currentTimeMillis()
            val minClickInterval = 1000L // 600ms还会触发, 1秒比较稳定
            if (!isNavigating && (currentTime - lastBackClickTime) > minClickInterval) {
                lastBackClickTime = currentTime
                isNavigating = true
                onBackPressedDispatcher?.onBackPressedDispatcher?.onBackPressed()
                // 延迟重置状态，确保防重生效
                coroutineScope.launch {
                    kotlinx.coroutines.delay(minClickInterval)
                    isNavigating = false
                }
            }
        }
    }

    // 监听标题编辑状态变化
    LaunchedEffect(state.isEditing) {
        onTitleEditingStateChanged(state.isEditing)
        
        // 如果处于编辑状态，确保输入框获得焦点并显示键盘
        if (state.isEditing) {
            FocusRequestHelper.requestFocusWithRetry(
                scope = coroutineScope,
                focusRequester = focusRequester,
                keyboardController = null,
                canProceed = { state.isEditing && !forceExitEditing }
            )
        }
    }

    // 监听强制退出编辑
    LaunchedEffect(forceExitEditing) {
        if (forceExitEditing && state.isEditing) {
            // 立即取消所有正在进行的焦点请求
            FocusRequestHelper.cancelCurrentFocusRequest()
            saveTitle()
            titleEditViewModel.resetForceExitFlag()
        }
    }

    // 处理返回按键
    BackHandler(enabled = state.isEditing) {
        saveTitle()
    }
    
    // 监听生命周期，在Activity恢复时恢复焦点
    // 但在强制退出编辑时不进行焦点恢复，避免冲突
    DisposableEffect(lifecycleOwner, state.isEditing, forceExitEditing) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME && state.isEditing && !forceExitEditing) {
                // Activity恢复且处于编辑状态，但非强制退出时，请求焦点
                FocusRequestHelper.requestFocusWithRetry(
                    scope = coroutineScope,
                    focusRequester = focusRequester,
                    keyboardController = null,
                    initialDelay = 500, // 给更多时间让UI稳定
                    canProceed = { state.isEditing && !forceExitEditing }
                )
            }
        }
        
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .statusBarsPadding()
            .height(TclTheme.dimens.navigationBarHeight),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧返回按钮区域
        NavigationBackButton(
            onBackClick = handleBackClick,
            btnSize = TclTheme.dimens.btnSize,
            iconSize = TclTheme.dimens.iconSize
        )

        // 左侧标题区域
        EditableTitle(
            state = state,
            defaultTitle = defaultTitle,
            onTitleChanged = onTitleChanged,
            onStateChanged = { newState ->
                state = newState
                onNavigationBarStateChanged?.invoke(newState)
            },
            onSave = saveTitle,
            focusRequester = focusRequester,
            keyboardController = null,
            focusManager = focusManager,
            modifier = Modifier.weight(1f),
            onUserStartedEditing = onUserStartedEditing
        )

        // 右侧功能区域
        NavigationActionArea(
            state = state,
            onStateChanged = { newState -> 
                state = newState
                onNavigationBarStateChanged?.invoke(newState)
            },
            density = density,
            modifier = Modifier,
            onDeleteNote = {} // 保留接口兼容性，但不处理
        )

    }
}


private fun handleTitleSave(
    state: EditableTitleNavigationBarState,
    defaultTitle: String,
    onTitleChanged: (String) -> Unit,
    updateState: (EditableTitleNavigationBarState) -> Unit,
    focusManager: androidx.compose.ui.focus.FocusManager,
    keyboardController: androidx.compose.ui.platform.SoftwareKeyboardController?
) {
    // 取消所有正在进行的焦点请求，防止键盘弹出
    FocusRequestHelper.cancelCurrentFocusRequest()
    
    val finalTitle = state.title.ifBlank { defaultTitle }
    onTitleChanged(finalTitle)
    updateState(
        state.copy(
            isEditing = false,
            title = finalTitle,
            hasUserEditedTitle = finalTitle != defaultTitle
        )
    )
    focusManager.clearFocus()
    keyboardController?.hide()
} 