package com.tcl.ai.note.handwritingtext.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.runtime.derivedStateOf
import androidx.compose.ui.platform.LocalDensity
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.controller.NoteDataSaveController
import com.tcl.ai.note.handwritingtext.richtext.utils.NoteContentUtil
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.edit.EraserPressIndicator
import com.tcl.ai.note.handwritingtext.ui.edit.container.RichTextToolbarContainer
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.BottomMenuBar
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarConfig
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.RichTextFormatToolbar
import com.tcl.ai.note.handwritingtext.ui.other.MaskForTitleEditOnContent
import com.tcl.ai.note.handwritingtext.ui.edit.container.RichTextToolbarContainer
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group.LassoEditPopupArea
import com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.group.LassoRotatePopupArea
import com.tcl.ai.note.handwritingtext.ui.popup.ShowDataLoadingPopup
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.TitleEditViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.handwritingtext.track.AnalyticsHandWritingTextModel

@Composable
fun EditContent(
    noteId: Long?,
    modifier: Modifier = Modifier,
    isPen: Boolean = false,
    richTextViewModel: RichTextViewModel2 = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
    titleEditViewModel: TitleEditViewModel = hiltViewModel(),
    penToolbarViewModel: PenToolbarViewModel = hiltViewModel()
) {
    //使用LocalConfiguration来检测配置变化，确保配置变化时也能重新执行
    //排除暗黑模式和屏幕旋转的配置变化，避免不必要的重新加载
    val configuration = LocalConfiguration.current
    val filteredConfiguration by remember {
        derivedStateOf {
            // 创建一个过滤后的配置标识，排除orientation和uiMode的变化
            // 只有其他配置变化时才会触发重新执行
            configuration.run {
                // 使用其他重要的配置属性作为标识，排除orientation和uiMode
                "${screenWidthDp}_${screenHeightDp}_${densityDpi}_${fontScale}_${smallestScreenWidthDp}_${locale}"
            }
        }
    }
    LaunchedEffect(noteId, filteredConfiguration) {
        //配置变化时noteId可能为null，但ViewModel中可能已有有效的noteId
        //优先使用传入的noteId，如果为null且ViewModel中有有效noteId，则使用ViewModel中的
        val effectiveNoteId=noteId?:richTextViewModel.mNoteId
        richTextViewModel.createOrGetNote(effectiveNoteId)
    }

    // 初始化埋点 - 仅在这2种viewmodel都可用时加载
    LaunchedEffect(richTextViewModel, textAndDrawViewModel) {
        AnalyticsHandWritingTextModel.loadRichTextViewModel2(richTextViewModel, textAndDrawViewModel)
    }
    LaunchedEffect(penToolbarViewModel) {
        AnalyticsHandWritingTextModel.loadPenToolbarViewModel(penToolbarViewModel)
    }
    LaunchedEffect(richTextViewModel.mNoteId) {
        suniaDrawViewModel.loadDrawStroke(richTextViewModel.mNoteId)
    }

    // 根据新建模式和加载已有数据模式设置编辑模式
    LaunchedEffect(richTextViewModel.uiState.collectAsState().value.isNewNote) {
        val uiState = richTextViewModel.uiState.value
        // textAndDrawViewModel.editMode != EditMode.DRAW 说明不是首次进入编辑界面，不是新建模式，不需要改变编辑模式
        if (!isPen && textAndDrawViewModel.editMode != EditMode.DRAW) { // 只有在非手绘模式下才设置文本编辑模式
            if (uiState.isNewNote || NoteContentUtil.isContentEmpty(uiState)) {
                // 新建模式：设置为富文本编辑模式
                textAndDrawViewModel.editMode = EditMode.TEXT
            } else {
                // 加载已有数据模式：设置为预览模式
                textAndDrawViewModel.editMode = EditMode.PREVIEW
            }
        }
    }

    LaunchedEffect(Unit) {
        if (isPen) {
            menuBarViewModel.switchToDrawingMode()
            textAndDrawViewModel.editMode = EditMode.DRAW
            suniaDrawViewModel.switchBrush(suniaDrawViewModel.penPropState.value)
        }
    }

//    val suniaDrawLoadingState by suniaDrawViewModel.drawLoadFinishState.collectAsState()
//    if (!suniaDrawLoadingState) {
//        ShowDataLoadingPopup()
//    }
    val density = LocalDensity.current
    val lassoEditState by suniaDrawViewModel.lassoEditState.collectAsState()
    val lassoRotateState by suniaDrawViewModel.lassoRotateState.collectAsState()

    var popupContent: (@Composable (areaHeight:Int) -> Unit)? by remember { mutableStateOf(null) }


    Box(modifier = modifier.fillMaxSize()) {
        TextAndDrawBoard(
            modifier = Modifier.fillMaxSize(),
            noteId = noteId,
        )
        // 橡皮擦按压指示器层级调整, 必须比手机端菜单栏低
        EraserPressIndicator()
        if (isTablet) {
            // 平板端：底部富文本栏和原有菜单栏
            RichTextToolbarContainer(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    // 补充白底，避免手写输入法栏造成底部镂空
                    .background(TclTheme.colorScheme.secondaryBackground)
                    .imePadding()
            ) {
                RichTextFormatToolbar()
            }
        } else {
            // 手机端，合并顶部菜单+底部工具栏
            BottomMenuBar(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter),
                config = MenuBarConfig(
                    setPopupComposable = { popup -> popupContent = popup }
                )
            )
        }

        popupContent?.let { popupComposable ->
            var containerHeight by remember { mutableIntStateOf(0) }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .onSizeChanged { size ->
                        containerHeight = size.height
                    }
            ){
                if(containerHeight > 0){
                    popupComposable((containerHeight - TclTheme.dimens.menuBarHeight.toPx).toInt())
                }
            }
        }
        lassoEditState?.let {
            if (it.isEdit) {
                LassoEditPopupArea(
                    it,
                    onStateChanged = {

                    },
                    density,
                    onDelete = {
                        suniaDrawViewModel.deleteEditSelect()
                    }
                )
            }
        }
        lassoRotateState?.let {
            LassoRotatePopupArea(
                it,
                onStateChanged = {

                },
                density,
            )

        }

        // 遮罩的层级是最高的
        MaskForTitleEditOnContent(titleEditViewModel)
    }
}

