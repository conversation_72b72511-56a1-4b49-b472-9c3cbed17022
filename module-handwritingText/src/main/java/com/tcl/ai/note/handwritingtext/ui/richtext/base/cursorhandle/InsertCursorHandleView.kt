package com.tcl.ai.note.handwritingtext.ui.richtext.base.cursorhandle

import android.annotation.SuppressLint
import android.graphics.PointF
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.WindowManager
import androidx.core.content.ContextCompat
import com.tcl.ai.note.handwritingtext.ui.richtext.base.BaseRichTextEditView
import com.tcl.ai.note.handwritingtext.ui.richtext.base.cursorhandle.BaseCursorHandleView.Companion.CursorType
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorHeight
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorPointF
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorWidth
import com.tcl.ai.note.utils.Logger

@SuppressLint("ClickableViewAccessibility")
class InsertCursorHandleView(
    private val editText: BaseRichTextEditView,
    private var isDraggable: Boolean = true,
) : BaseCursorHandleView(editText, isDraggable) {
    // 拖动柄被点击
    var onClick: () -> Unit = {}
    // 拖动柄消失
    var onDismiss: () -> Unit = {}
    // 插入拖动柄移动
    var onInsertHandleMovePosition: (pos: Int) -> Unit = {}

    init {
        cursorHandleImageView.setImageDrawable(
            getCursorDrawable(editText.context, CursorType.INSERT)
        )
        val cursorColorInt = ContextCompat.getColor(editText.context, com.tcl.ai.note.base.R.color.text_field_border)
        cursorHandleImageView.setColorFilter(cursorColorInt)
        contentView.setOnTouchListener { v, e ->
            gestureDetector.onTouchEvent(e)
            dragEventListener.onTouch(v, e)
        }
    }

    private val gestureListener = object : GestureDetector.SimpleOnGestureListener() {
        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            onClick()
            Logger.d(TAG, "onSingleTapConfirmed, startActionMode: customInsertionActionModeCallback")
            return true
        }
    }
    private val gestureDetector = GestureDetector(editText.context, gestureListener)

    /**
     * 修正光标拖动柄（水滴）位置，获取光标正下方位置(左对齐)
     */
    override fun calculateCursorPoint(): PointF {
        val cursorPoint = editText.getCursorPointF(editText.selectionStart)
        val cursorHeight = editText.getCursorHeight(editText.selectionStart)
        val revertLogicX = cursorPoint.x - width / 2 + editText.getCursorWidth() / 2
        val revertLogicY = cursorPoint.y + editTextTop + cursorHeight
        return PointF(revertLogicX, revertLogicY)
    }

    /**
     * 插入拖动柄需要居中，所以偏移半个width
     */
    override fun calculateHandleDrawableOffset(): PointF {
        return PointF(-(width / 2).toFloat(), 0f)
    }

    /**
     * 移动光标
     */
    override fun moveAndGetCursorPosition(eventX: Float, eventY: Float): Int {
        val cursorPosition = editText.getOffsetForPosition(eventX, eventY)
        // 移动插入光标
        if (editText.selectionStart == editText.selectionEnd && cursorPosition != editText.selectionStart) {
            editText.setSelection(cursorPosition)
            onInsertHandleMovePosition(cursorPosition)
            vibrate()
        }
        return cursorPosition
    }

    override fun dismiss() {
        super.dismiss()
        onDismiss()
    }

    companion object {
        private const val TAG = "InsertCursorHandleView"
    }
}