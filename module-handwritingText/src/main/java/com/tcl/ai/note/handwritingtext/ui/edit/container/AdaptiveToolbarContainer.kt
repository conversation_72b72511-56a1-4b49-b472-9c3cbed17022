package com.tcl.ai.note.handwritingtext.ui.edit.container

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.HorizontalLine

/**
 * 自适应工具栏容器
 * 键盘弹出时依赖 Scaffold 自动适配，键盘消失时添加导航栏高度的底部边距
 *
 * @param isVisible 是否显示工具栏
 * @param modifier 修饰符
 * @param showDivider 是否显示分割线
 * @param content 工具栏内容
 */
@Composable
fun AdaptiveToolbarContainer(
    isVisible: Boolean,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true,
    content: @Composable () -> Unit
) {
    if (isVisible) {
        Column(
            modifier = modifier.padding(bottom = 0.dp)
        ) {
            if (showDivider) {
                HorizontalLine()
            }
            content()
        }
    }
}

/**
 * 富文本工具栏容器的便捷函数
 * 专门用于富文本格式工具栏的场景
 *
 * @param modifier 修饰符
 * @param toolbar 工具栏内容
 */
@Composable
fun RichTextToolbarContainer(
    modifier: Modifier = Modifier,
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
    isFixed: Boolean = true,
    toolbar: @Composable () -> Unit
) {
    val menuBarState by menuBarViewModel.menuBarState.collectAsState()
    if (isFixed || textAndDrawViewModel.editMode != EditMode.DRAW) {
        AdaptiveToolbarContainer(
            isVisible = menuBarState.isKeyboardActive || !isTablet, // 手机端是固定的，无论是富文本还是手绘编辑模式
            modifier = modifier,
            showDivider = true,
            content = toolbar
        )
    } else {
        toolbar()
    }
}
