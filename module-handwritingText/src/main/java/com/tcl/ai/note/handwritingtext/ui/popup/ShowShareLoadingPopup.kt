package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tct.theme.core.designsystem.component.TclLoadingDialog

@Composable
fun ShowShareLoadingPopup(
    modifier: Modifier = Modifier,
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
) {
    val isShow by textAndDrawViewModel.shareLoadingState.collectAsState()
    TclLoadingDialog(
        show = isShow,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
        ),
        onDismissRequest = {
            textAndDrawViewModel.cancelShare()
        },
    )
}