package com.tcl.ai.note.handwritingtext.ui.edit.navigationbar.utils

import android.content.Context
import android.graphics.RectF
import android.net.Uri
import androidx.core.net.toUri
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.utils.ImageUtils
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.Logger
import java.io.File

object ImageInsertHelper {
    
    /**
     * 插入图片到画板
     */
    fun insertImageToDrawBoard(
        context: Context, 
        uri: Uri, 
        drawRectF: RectF, 
        suniaDrawViewModel: SuniaDrawViewModel,
        richTextViewModel: RichTextViewModel2
    ) {
        val result = ImageUtils.calculateImageInsertRect(context, uri, drawRectF) ?: return
        val (imagePath, showRectF) = result
        
        // 插入到绘图画板
        suniaDrawViewModel.insertBitmap(imagePath, showRectF)

        // TODO 笔记保存还有问题（2025年7月23日测试保存不了）
        // 同时更新RichTextViewModel2的图片列表，确保埋点统计正确
        val currentImages = richTextViewModel.uiState.value.images.toMutableList()
        // 将String路径转换为Uri
        val imageUri = File(imagePath).toUri()
        Logger.d("insertImageToDrawBoard", "imagePath: $imagePath imageUri: $imageUri uri :${uri.path} ")

        currentImages.add(EditorContent.ImageBlock(uri = uri))
        richTextViewModel.onImagesChanged(currentImages)
    }
}