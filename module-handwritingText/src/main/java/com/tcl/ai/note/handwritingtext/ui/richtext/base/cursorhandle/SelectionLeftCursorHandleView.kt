package com.tcl.ai.note.handwritingtext.ui.richtext.base.cursorhandle

import android.annotation.SuppressLint
import android.graphics.PointF
import android.view.MotionEvent
import androidx.core.content.ContextCompat
import com.tcl.ai.note.handwritingtext.ui.richtext.base.BaseRichTextEditView
import com.tcl.ai.note.handwritingtext.ui.richtext.base.cursorhandle.BaseCursorHandleView.Companion.CursorType
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorHeight
import com.tcl.ai.note.handwritingtext.ui.richtext.base.getCursorPointF

@SuppressLint("ClickableViewAccessibility")
class SelectionLeftCursorHandleView(
    private val editText: BaseRichTextEditView,
    private var isDraggable: Boolean = true,
) : BaseCursorHandleView(editText, isDraggable) {
    // 左拖动柄移动
    var onLeftSelectionMovePosition: (pos: Int) -> Unit = {}
    // 拖动结束
    var onLeftSelectionMovePositionEnd: (pos: Int) -> Unit = {}

    init {
        cursorHandleImageView.setImageDrawable(
            getCursorDrawable(editText.context, CursorType.LEFT_SELECTION)
        )
        val cursorColorInt = ContextCompat.getColor(editText.context, com.tcl.ai.note.base.R.color.text_field_border)
        cursorHandleImageView.setColorFilter(cursorColorInt)
        contentView.setOnTouchListener { v, e ->
            val isInterrupted = dragEventListener.onTouch(v, e)
            if (e.actionMasked == MotionEvent.ACTION_UP) {
                onLeftSelectionMovePositionEnd(editText.selectionStart)
            }
            return@setOnTouchListener isInterrupted
        }
    }

    /**
     * 修正光标拖动柄（水滴）位置，获取光标正下方位置(左对齐)
     */
    override fun calculateCursorPoint(): PointF {
        val cursorPoint = editText.getCursorPointF(editText.selectionStart)
        val cursorHeight = editText.getCursorHeight(editText.selectionStart)
        val revertLogicX = cursorPoint.x - width.toFloat() + paddingHorizontal
        val revertLogicY = cursorPoint.y + editTextTop + cursorHeight
        return PointF(revertLogicX, revertLogicY)
    }

    /**
     * 左拖动柄需要偏移一个width的位置
     */
    override fun calculateHandleDrawableOffset(): PointF {
        return PointF(-width.toFloat(), 0f)
    }

    /**
     * 移动光标
     */
    override fun moveAndGetCursorPosition(eventX: Float, eventY: Float): Int {
        var cursorPosition = editText.getOffsetForPosition(eventX, eventY)
        // 左选区扩展
        if (editText.selectionStart != editText.selectionEnd
            && cursorPosition != editText.selectionStart
            && cursorPosition != editText.selectionEnd
        ) {
            editText.setSelection(cursorPosition, editText.selectionEnd)
            onLeftSelectionMovePosition(cursorPosition)
            vibrate()
        }
        return cursorPosition
    }

    companion object {
        private const val TAG = "InsertCursorHandleView"
    }
}