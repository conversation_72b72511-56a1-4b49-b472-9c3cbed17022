package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.group

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.ui.widget.CustomRadioButton
import com.tcl.ai.note.handwritingtext.vm.menu.ColorGroupViewModel
import com.tcl.ai.note.handwritingtext.ui.popup.MenuColorPalettePopup
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toComposeColor
import com.tcl.ai.note.widget.VerticalLine

/**
 * 顶部菜单栏三色菜单栏
 */
@Composable
fun TabletMenuColorGroup(
    viewModel: ColorGroupViewModel = hiltViewModel(),
    isDrawMode:Boolean = false,
    onConfirm:(penColor: PenColor) ->Unit,
    onChangeDrawMode:() ->Unit,
    setPopupComposable: ((@Composable () -> Unit)?) -> Unit,
){
    val dimens = getGlobalDimens()
    val colorGroupState by viewModel.colorGroupState.collectAsState()
    val collectColors by viewModel.collectColors.collectAsState()

    LaunchedEffect(colorGroupState.selectedColorIdx){
        colorGroupState.getSelectedColor()?.let { color ->
            onConfirm(color)
        }

    }

    var lastTimeDismiss = 0L
    val onColorClick: (colorIdx: Int,menuBarItem: MenuBarItem) -> Unit = { colorIdx, menuBarItem ->
        viewModel.updateSelColorIdx(colorIdx)
        // 点击颜色总是要切换到笔刷模式（无论当前是什么模式）
        onChangeDrawMode()
        if(menuBarItem.isChecked){
            val offTime = System.currentTimeMillis() - lastTimeDismiss
            if(offTime> 200){
                val curPenColor = colorGroupState.getSelectedColor()
                curPenColor?.let {
                    setPopupComposable({
                        MenuColorPalettePopup(
                            menuBarItem =menuBarItem,
                            onConfirm = { color ->
                                viewModel.updateRecentColor(colorIdx,color)
                                onConfirm(color)
                                // 色盘确认时也要切换到笔刷模式
                                onChangeDrawMode()
                            },
                            collectColors = collectColors,
                            curPenColor =curPenColor,
                            onDismissRequest = {
                                lastTimeDismiss = System.currentTimeMillis()
                                setPopupComposable(null)
                            })
                    })
                }

            }else{
                setPopupComposable(null)
            }
        }
    }
    val menuColorItems = mutableListOf<MenuBarItem.BrushColor>().apply {
        colorGroupState.recentColors.forEachIndexed { index, penColor ->
            add(MenuBarItem.BrushColor(
                id = index,
                menuType= MenuBar.COLOR,
                btnSize =dimens.btnSize,
                color =penColor.color.toArgb(),
                isSelected=colorGroupState.selectedColorIdx == index && isDrawMode,
                onSelected = onColorClick,
                customContent = { modifier,item ->
                    val colorItem = item as? MenuBarItem.BrushColor
                    colorItem?.let {
                        MenuColorCircle(modifier = modifier,colorItem = item)
                    }
                }))
        }
    }
    Row(
        modifier = Modifier.height(dimens.menuBarHeight),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ){
        VerticalLine(
            modifier = Modifier.height(20.dp),
            color = colorResource(R.color.bg_outline_line_dark)
        )
        Spacer(Modifier.width(12.dp))
        menuColorItems.forEachIndexed { index, item ->
            val modifierPos = Modifier.onGloballyPositioned { layoutCoordinates ->
                val position = layoutCoordinates.localToWindow(Offset.Zero)
                item.position =position
            }
            if (item.customContent != null) {
                item.customContent?.invoke(modifierPos,item)
                if(index != menuColorItems.lastIndex){
                    Spacer(Modifier.width(8.dp))
                }


            }
        }
    }
}


@Composable
internal fun MenuColorCircle(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    modifier: Modifier,
    colorItem:MenuBarItem.BrushColor){

    val context = LocalContext.current
    Box(
        modifier = modifier.size(colorItem.btnSize).semantics {
            this.contentDescription =context.getString(R.string.edit_bottom_menu_color_select)
            this.role = Role.Button
        },
        contentAlignment = Alignment.Center
    ){
        CustomRadioButton(
            modifier = Modifier.invisibleSemantics(),
            onClick = {
                colorItem.onClick(colorItem)
            },
            selected = colorItem.isSelected,
            color =  colorItem.color.toComposeColor().run {
                isDarkTheme.judge(
                    positiveValue = this.inverseColor(),
                    negativeValue = this
                )
            },
            outerSize =20.dp
        )
    }




}