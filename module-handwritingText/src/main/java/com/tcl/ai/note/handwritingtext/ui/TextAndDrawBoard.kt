package com.tcl.ai.note.handwritingtext.ui

import android.app.Activity
import android.util.Log
import android.widget.EditText
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.layout.positionOnScreen
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.controller.NoteDataSaveController
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.repo.idToEntPath
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.PADDING_VERTICAL_PHONE_DP
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.PADDING_VERTICAL_TABLET_DP
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MeshStyle
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.richtext.widget.MESH_CELLS
import com.tcl.ai.note.handwritingtext.ui.widget.ScaleIndicator
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import com.tcl.ai.note.handwritingtext.vm.menu.EraserViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isFileExists
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.rememberImeHeightPxWithoutNav
import com.tcl.ai.note.utils.screenSizeMin
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.DisposableEffectWithLifecycle
import com.tct.theme.core.designsystem.component.TclLoadingIndicator
import java.lang.ref.WeakReference

private const val TAG = "TextAndDrawBoard"

@Composable
fun SuniaDrawBoard(
    modifier: Modifier = Modifier,
    noteId: Long? = null,
    richTextViewModel: RichTextViewModel2 = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    eraserViewModel: EraserViewModel= hiltViewModel(),
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
) {
    var textAndDrawBoardLayout: TextAndDrawBoardLayout? by remember { mutableStateOf(null) }
    val focusManager = LocalFocusManager.current
    val isDarkTheme = isSystemInDarkTheme()
    var lastIsDarkTheme by remember { mutableStateOf(false) }

    // 获取键盘状态，需要在DisposableEffectWithLifecycle之前声明
    val menuBarState by menuBarViewModel.menuBarState.collectAsState()
    val isKeyboardActive by remember { derivedStateOf { menuBarState.isKeyboardActive } }
    val imeHeight = rememberImeHeightPxWithoutNav()


    LaunchedEffect(Unit) {
        // 设置SDK更新回调，确保初始化时能同步设置到SDK(按键擦除 需要用到橡皮擦模式及大小，故设置回调前置确保获取到的橡皮擦大小和尺寸正常)
        eraserViewModel.setSdkUpdateCallback { size, eraserMode ->
            Logger.d("SuniaDrawBoard","setSdkUpdateCallback, size:$size, eraserMode:$eraserMode ")
            suniaDrawViewModel.updateEraserData(eraserMode, size)
        }
    }

    DisposableEffectWithLifecycle(
        onPause = {
            Logger.d("SuniaDrawBoard", "onPause - saving edit state")
            // 退出到后台，强制保存数据，避免分屏等触发activity重组导致数据丢失
            GlobalContext.applicationScope.launchIO {
                Logger.d(TAG, "onPause, invoke force save")
                NoteDataSaveController.forceSave().await()
            }
            // 保存当前编辑状态
            val currentLayout = textAndDrawBoardLayout
            if (currentLayout != null) {
                val isKeyboardActiveFromController = currentLayout.isKeyboardActive()
                val hasFocus = currentLayout.hasRichTextFocus()
                // 使用MenuBarViewModel的状态和IME高度来更准确地判断键盘可见性
                val isKeyboardVisible = isKeyboardActive && hasFocus && imeHeight > 0
                val cursorPosition = currentLayout.getRichTextCursorPosition()
                textAndDrawViewModel.saveEditState(isKeyboardActiveFromController, hasFocus, isKeyboardVisible, cursorPosition)
                Logger.d("SuniaDrawBoard", "Saved edit state: keyboardActive=$isKeyboardActiveFromController, focus=$hasFocus, keyboardVisible=$isKeyboardVisible, imeHeight=$imeHeight, cursor=$cursorPosition")
            }
            focusManager.clearFocus()
        },
        onResume = {
            Logger.d("SuniaDrawBoard", "onResume - checking for edit state restoration")

            // 检查是否需要恢复编辑状态
            if (textAndDrawViewModel.shouldRestoreEditState()) {
                val savedState = textAndDrawViewModel.getSavedEditState()
                Logger.d("SuniaDrawBoard", "Restoring edit state: $savedState")

                // 延迟恢复，确保UI完全加载
                textAndDrawBoardLayout?.postDelayed({
                    try {
                        if (savedState != null && savedState.editMode == EditMode.TEXT) {
                            // 恢复文本编辑模式
                            textAndDrawViewModel.editMode = EditMode.TEXT

                            // 如果需要显示键盘，则切换到文本编辑模式
                            if (savedState.isKeyboardVisible) {
                                menuBarViewModel.switchToTextEditMode()
                            }

                            textAndDrawBoardLayout?.restoreEditState(savedState.cursorPosition, savedState.isKeyboardVisible)
                            Logger.d("SuniaDrawBoard", "Edit state restored successfully: keyboardVisible=${savedState.isKeyboardVisible}")
                        }
                    } catch (e: Exception) {
                        Logger.e("SuniaDrawBoard", "Failed to restore edit state: ${e.message}")
                    } finally {
                        // 清除保存的状态
                        textAndDrawViewModel.clearSavedEditState()
                    }
                }, 300) // 延迟300ms确保UI稳定
            }
        },
    )

    // 添加DisposableEffect来处理清理
    DisposableEffect(Unit) {
        Logger.d("EditContent", "EditContent composition started")
        onDispose {
            Logger.d("EditContent", "EditContent disposing, starting comprehensive cleanup")
            try {
                // 清理RichTextEventManager中的事件状态
                RichTextEventManager.clearStyleEvent()
                RichTextEventManager.clearOperateEvent()
                RichTextEventManager.restoreToolBarStyleState()

                Logger.d("EditContent", "EditContent cleanup completed successfully")

            } catch (e: Exception) {
                Logger.e("EditContent", "Error during EditContent cleanup: ${e.message}")
            }
        }
    }

    LaunchedEffect(isDarkTheme) {
        if (isDarkTheme != lastIsDarkTheme) {
            lastIsDarkTheme = isDarkTheme
            textAndDrawBoardLayout?.changeDarkTheme(isDarkTheme)
        }
    }

    Logger.d("TextAndDrawBoard", "compose init")

    val toolBarHeightPx = with(LocalDensity.current) { getGlobalDimens().richTextToolBarHeight.roundToPx() }

    AndroidView(
        factory = { context ->
            Logger.i(
                "SuniaDrawBoard",
                "AndroidView[TextAndDrawBoardLayout] create!"
            )
            TextAndDrawBoardLayout(
                context = context,
                richTextViewModel = richTextViewModel,
                suniaDrawViewModel = suniaDrawViewModel,
                textAndDrawViewModel = textAndDrawViewModel,
                menuBarViewModel = menuBarViewModel,
                needToLoadInFirstEnter = noteId != null && noteId.idToEntPath().isFileExists()
            ).also { textAndDrawBoardLayout = it }
        },
        modifier = modifier
            .padding(
                bottom = if (isTablet) 0.dp
                else getGlobalDimens().richTextToolBarHeight
            ),
        update = { view ->
            Logger.i(
                "SuniaDrawBoard",
                "AndroidView[TextAndDrawBoardLayout] update! isKeyboardActive: $isKeyboardActive"
            )
            view.setupRichTextBottomPadding(
                if (isKeyboardActive){
                    if (isTablet) toolBarHeightPx + imeHeight
                    else imeHeight
                } else 0
            )
        }
    )
}

@Composable
fun TextAndDrawBoard(
    modifier: Modifier = Modifier,
    noteId: Long? = null,
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    richTextViewModel: RichTextViewModel2 = hiltViewModel(),
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
) {
    val loading by textAndDrawViewModel.onLoading.collectAsState()
    val suniaDrawLoadFinishState by suniaDrawViewModel.drawLoadFinishState.collectAsState()
    // 缩放信息
    val matrixInfo by textAndDrawViewModel.matrixInfoState.collectAsState()
    val richTextState by richTextViewModel.uiState.collectAsState()
    Box(
        modifier = modifier
            .wrapContentSize()
            .background(
                isDarkTheme.judge(
                    Color(0xFF1A1A1A),
                    Color(0xFFF5F6F7)

                )
            )
    ) {
        // 背景
        val fontScale = LocalDensity.current.fontScale
        val width =(screenSizeMin * matrixInfo.scale).toInt().px2dp.dp
        val lineSpaceDp = (screenSizeMin * matrixInfo.scale).toInt().px2dp / MESH_CELLS

        MeshStyle(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .fillMaxHeight(),
            width = width,
           // offsetY = isTablet.judge(PADDING_VERTICAL_TABLET_DP, PADDING_VERTICAL_PHONE_DP).dp.toPx * matrixInfo.scale,
            offsetY = 0f,
            getTranslation = {
                FloatArray(2).apply {
                    this[0] = matrixInfo.offsetX
                    this[1] = matrixInfo.offsetY
                }
            },
            bgMode = richTextState.bgMode,
            bgColor =richTextState.displayBgColor,
            lineSpaceDp =lineSpaceDp
        )




        // 画板
        SuniaDrawBoard(
            modifier = Modifier.wrapContentSize(),
            noteId = noteId,
        )


        // 缩放指示，不能使用popup，会导致手绘层bug
        ScaleIndicator(
            scaleState = matrixInfo.scale,
            modifier = Modifier.align(Alignment.TopEnd),
        )

        if (loading) {
            val context = LocalContext.current
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxSize()
                    .background(TclTheme.colorScheme.primaryBackground)
                    .imePadding()
                    .align(Alignment.Center)
            ) {
                DisposableEffect(Unit) {
                    // 如果当前焦点的view时EditText， 则清除焦点。避免显示拖动柄
                    val editText = ((context as? Activity)?.currentFocus as? EditText)
                    editText?.clearFocus()
                    editText?.setSelection(editText.selectionStart)
                    val viewRef = WeakReference(editText)
                    onDispose {
                        viewRef.get()?.requestFocus()
                    }
                }
                TclLoadingIndicator(
                    color = TclTheme.colorScheme.loadingIndicatorColor,
                    size = 48.dp,
                )
            }
        }
    }
}