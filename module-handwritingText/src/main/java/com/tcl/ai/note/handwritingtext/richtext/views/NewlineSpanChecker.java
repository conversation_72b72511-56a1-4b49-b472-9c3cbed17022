package com.tcl.ai.note.handwritingtext.richtext.views;

import android.text.Editable;

import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan;

public class NewlineSpanChecker {

    /**
     * 获取段落起始位置
     *
     * 从指定位置向前查找最近的连续两个换行符（\n\n），获取当前段落的起始位置
     * 段落起始位置定义为：连续两个换行符中第二个换行符的后一位（即\n\n之后的第一个位置）
     * @param editable 目标Editable对象
     * @param startPos 起始查找位置（从该位置向前查找）
     * @return 段落起始位置，未找到则返回0（文本起始位置）
     */
    public static int findParagraphStart(Editable editable, int startPos) {
        // 空文本或位置不合法，返回0
        if (editable == null || startPos <= 0) {
            return 0;
        }

        int textLength = editable.length();
        // 限制查找范围在有效文本内
        int searchEnd = Math.min(startPos, textLength);

        // 从起始位置向前查找连续两个换行符
        for (int i = searchEnd - 1; i >= 1; i--) {
            // 检查当前位置和前一个位置是否都是换行符
            if (editable.charAt(i) == '\n' && editable.charAt(i - 1) == '\n') {
                // 找到连续两个换行符，段落起始位置为第二个换行符的后一位
                return i + 1;
            }
        }

        // 未找到连续两个换行符，说明当前段落从文本开头开始
        return 0;
    }


    /**
     * 判断从指定位置开始是否存在段落结束标志
     * 满足以下任一条件返回true：
     * 1. 找到连续两个换行符（\n\n）
     * 2. 到达当前段落的末尾（后续无内容）
     * 3. 到达整个文本的末尾
     * @param editable 目标Editable对象
     * @param startPos 起始查找位置
     * @return 满足条件返回true，否则返回false
     */
    public static int findParagraphEnd(Editable editable, int startPos) {
        // 空文本返回-1（无内容可处理）
        if (editable == null) {
            return -1;
        }

        int textLength = editable.length();

        // 起始位置超出文本范围，返回文本最后位置（视为末尾）
        if (startPos >= textLength) {
            return textLength - 1;
        }

        // 从起始位置开始查找
        for (int i = startPos; i < textLength; i++) {
            // 条件1：找到连续两个换行符，返回第一个换行符位置
            if (i + 1 < textLength &&
                    editable.charAt(i) == '\n' &&
                    editable.charAt(i + 1) == '\n') {
                return i;
            }

            // 条件2：到达文本末尾，返回最后一个字符位置
            if (i == textLength - 1) {
                return i;
            }
        }

        // 未找到符合条件的位置（理论上不会走到这里）
        return -1;
    }

    /**
     * 检查指定位置是否为行首第一个字符，且前一位（若存在）是否为Span
     * @param editable 目标Editable对象
     * @param position 要检查的位置
     * @return 满足条件返回true，否则返回false
     */
    public static boolean isLineStartWithPreviousSpan(Editable editable, int position) {
        // 空文本或位置不合法
        if (editable == null || position < 0 || position >= editable.length()) {
            return false;
        }

        // 条件1：检查是否为行首第一个字符
        boolean isLineStart = false;
        if (position == 0) {
            // 文本的第一个字符，视为行首
            isLineStart = true;
        } else {
            // 前一个字符是换行符，视为行首
            char previousChar = editable.charAt(position - 1);
            isLineStart = (previousChar == '\n');
        }

        if (!isLineStart) {
            return false;
        }

        // 条件2：检查前一位（若存在）是否为Span
        // 行首位置为0时，没有前一位，直接满足条件
        if (position == 0) {
            return true;
        }

        // 行首是因为前一位是换行符，检查换行符的前一位是否为Span
        int spanCheckPosition = position - 2;
        // 换行符前还有内容才需要检查
        if (spanCheckPosition >= 0) {
            // 检查换行符前一位是否有任何Span
            ListNumberSpan[] spans = editable.getSpans(spanCheckPosition, spanCheckPosition + 1, ListNumberSpan.class);
            return spans != null && spans.length > 0;
        } else {
            // 换行符是文本的第一个字符，前面没有内容，满足条件
            return true;
        }
    }

    /**
     * 判断指定索引位置后面是否紧接着就是Span（即索引+1位置是否被Span覆盖）
     * @param editable 目标Editable对象
     * @param index 指定的索引位置
     * @return 若索引后第一个位置存在Span则返回true，否则返回false
     */
    public static boolean hasImmediateSpanAfter(Editable editable, int index) {
        // 空文本或索引不合法
        if (editable == null) {
            return false;
        }

        int textLength = editable.length();
        // 索引超出范围，或索引是最后一个字符（后面没有内容）
        if (index < 0 || index >= textLength - 1) {
            return false;
        }

        // 要检查的位置：当前索引的后一位
        int checkPos = index + 1;

        // 检查该位置是否存在任何类型的Span
        ListNumberSpan[] spans = editable.getSpans(checkPos, checkPos + 1, ListNumberSpan.class);

        // 存在至少一个Span且是ListNumberSpan 即返回true
        return spans != null && spans.length > 0;
    }


    /**
     * 从指定位置开始查找换行符，若换行符后有Span则继续查找下一个换行符，
     * 直到找到后面没有Span的换行符，或到达文本末尾
     * @param editable 目标Editable对象
     * @param startPos 起始查找位置
     * @return 符合条件的换行符位置，未找到则返回-1
     */
    public static int findNewlineWithoutFollowingSpan(Editable editable, int startPos) {
        // 空文本或起始位置不合法
        if (editable == null || startPos < 0 || startPos >= editable.length()) {
            return -1;
        }

        int textLength = editable.length();
        int currentPos = startPos;

        // 循环查找换行符
        while (currentPos < textLength) {
            // 查找当前位置后的下一个换行符
            int newlinePos = findNextNewline(editable, currentPos);

            // 未找到更多换行符
            if (newlinePos == -1) {
                return -1;
            }

            // 检查这个换行符后面是否有Span
            boolean hasSpanAfter = hasSpanAfterNewline(editable, newlinePos);

            if (!hasSpanAfter) {
                // 找到后面没有Span的换行符，返回其位置
                return newlinePos;
            } else {
                // 换行符后面有Span，从下一个位置继续查找
                currentPos = newlinePos + 1;
            }
        }

        // 遍历完文本未找到符合条件的换行符
        return -1;
    }

    /**
     * 从指定位置查找下一个换行符
     * @param editable 目标Editable对象
     * @param startPos 起始位置
     * @return 换行符位置，未找到返回-1
     */
    private static int findNextNewline(Editable editable, int startPos) {
        int textLength = editable.length();
        for (int i = startPos; i < textLength; i++) {
            if (editable.charAt(i) == '\n') {
                return i;
            }
        }
        return -1;
    }

    /**
     * 检查换行符后面是否有Span
     * @param editable 目标Editable对象
     * @param newlinePos 换行符位置
     * @return 有Span返回true，否则返回false
     */
    private static boolean hasSpanAfterNewline(Editable editable, int newlinePos) {
        // 换行符是最后一个字符，后面没有内容
        if (newlinePos == editable.length() - 1) {
            return false;
        }

        int nextPos = newlinePos + 1;
        // 检查换行符后面是否有任何Span
        Object[] spans = editable.getSpans(nextPos, editable.length(), Object.class);
        return spans != null && spans.length > 0;
    }
}
