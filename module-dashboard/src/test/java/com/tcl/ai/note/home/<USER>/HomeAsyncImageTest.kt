package com.tcl.ai.note.home.components

import android.net.Uri
import androidx.core.net.toUri
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试 HomeAsyncImage 组件的图片加载逻辑
 */
class HomeAsyncImageTest {

    @Test
    fun testContentUriHandling() {
        // 测试 content://media/picker/ URI 的处理
        val contentUri = "content://media/picker/0/com.android.providers.media.photopicker/media/30"
        val uri = contentUri.toUri()
        
        assertNotNull("URI should not be null", uri)
        assertEquals("URI scheme should be content", "content", uri.scheme)
        assertTrue("URI should contain media picker path", 
            uri.toString().contains("media/picker"))
    }

    @Test
    fun testImageCacheKeyGeneration() {
        // 测试缓存键生成逻辑
        val imageUri = "content://media/picker/0/com.android.providers.media.photopicker/media/30"
        val modifyTime = 1234567890L
        val expectedCacheKey = "$imageUri$modifyTime"
        
        assertEquals("Cache key should match expected format", 
            expectedCacheKey, "$imageUri$modifyTime")
    }

    @Test
    fun testFallbackLogic() {
        // 测试回退逻辑
        val handwritingThumbnail: String? = null
        val firstPicture = "content://media/picker/0/com.android.providers.media.photopicker/media/30"
        val image = "file:///storage/emulated/0/Pictures/test.jpg"
        
        // 模拟 AsyncImageWithFallback 的逻辑
        val selectedUri = when {
            !handwritingThumbnail.isNullOrEmpty() -> handwritingThumbnail
            !firstPicture.isNullOrEmpty() -> firstPicture
            !image.isNullOrEmpty() -> image
            else -> null
        }
        
        assertEquals("Should select firstPicture when handwritingThumbnail is null", 
            firstPicture, selectedUri)
    }
}
