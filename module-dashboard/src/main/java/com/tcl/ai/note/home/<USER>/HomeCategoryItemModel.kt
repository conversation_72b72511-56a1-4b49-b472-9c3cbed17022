package com.tcl.ai.note.home.model

/**
 * 分类项数据模型
 */
data class HomeCategoryItemModel(
    val name: String?=null,
    val noteCounts: Int = 0, // 新增字段，表示分类下的笔记数量
    val categoryIcon: CategoryIcon?=null,
    val isSelected: Boolean = false,
    val isExpanded: Boolean = false, // 新增字段，表示是否展开
    val isLongClickSelected: Boolean = false, // 表示是长按选中 用于弹出菜单
    val id: String = "", // 分类ID
    val colorIndex: Int = 0, // 分类颜色索引
){
    // 是否可以操作，即是否是预置分类
    val isCanOperate: Boolean
        get() = !(id.isEmpty()||id==1L.toString())
}

data class CategoryIcon(
    val icon: Int,
    val color: Int? =null
)