package com.tcl.ai.note.home.components.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.model.CategoryIcon
import com.tcl.ai.note.home.model.HighlightInfo
import com.tcl.ai.note.home.model.HighlightRange
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.model.ThumbnailInfo
import com.tcl.ai.note.home.model.ThumbnailType

/**
 * 预览参数提供器 预览 HomeNoteItem 示例数据
 */
class HomeNoteItemPreviewParameterProvider : PreviewParameterProvider<HomeNoteItemModel> {
    override val values = sequenceOf(
        HomeNoteItemModel(
            noteId = "1",
            noteTitle = "Text Note",
            summary = "This is a sample text note",
            date = "2023-10-01",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.TEXT_CONTENT),
            highlightInfo = HighlightInfo(
                searchKeyword = "Text",
                titleHighlights = arrayListOf(HighlightRange(start = 0, end = 4)),
                contentHighlights = emptyList()
            ),
            categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised, R.color.category_color_YELLOW)
        ),
        HomeNoteItemModel(
            noteId = "1",
            noteTitle = "Text Note",
            summary = "This is a sample text note",
            date = "2023-10-01",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN),
            highlightInfo = HighlightInfo(searchKeyword = "Text", titleHighlights = arrayListOf(
                HighlightRange(start = 0, end = 4),
            )  ),
        ),
        HomeNoteItemModel(
            noteId = "2",
            noteTitle = "Audio Recording",
            summary = "Voice memo from meeting",
            date = "2023年10月20日 14:00",
            thumbnailInfo = ThumbnailInfo(
                type = ThumbnailType.AUDIO_ICON,
                showAudioIcon = true,
                hasAudio = true
            )
        ),
        HomeNoteItemModel(
            noteId = "3",
            noteTitle = "Screenshot",
            summary = "App interface design",
            date = "2023年10月20日",
            thumbnailInfo = ThumbnailInfo(type = ThumbnailType.FIRST_SCREEN),
            isChecked = true,

        ),

    )
}