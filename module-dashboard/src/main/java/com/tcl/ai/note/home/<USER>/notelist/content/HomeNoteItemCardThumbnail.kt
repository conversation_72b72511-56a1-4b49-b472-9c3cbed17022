package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.Image
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import coil3.compose.AsyncImage
import coil3.gif.GifDecoder
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.model.ThumbnailType
import com.tcl.ai.note.home.utils.rememberScaledCardSize
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getScreenSize
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.isTabletLandscape
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.components.BottomFadeBox

@Composable
internal fun CardTypeContent(note: HomeNoteItemModel) {
    Logger.d("HomeNoteItemCard", "thumbnailType:${note.thumbnailType}")
    val noteDes = note.summary?:""
    // 直接使用ViewModel中处理好的缩略图类型
    when (note.thumbnailType) {
        ThumbnailType.FIRST_SCREEN -> {
            // 显示第一屏信息（图片/手绘）
            AsyncImageWithFallback(note)
        }

        ThumbnailType.TEXT_CONTENT -> {
            // 显示富文本内容
            BottomFadeBox(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(12.dp)
                    .semantics {
                        contentDescription = noteDes
                    }
            ,
                fadeHeight = 24.dp
            ) {
                HomeRichTextPreview(
                    note = note,
                    modifier = Modifier.fillMaxSize(),
                    isNormalMode = true
                )
            }
        }

        ThumbnailType.AUDIO_ICON -> {
            // 显示录音图标
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_note_only_audio),
                    contentDescription = stringResource(R.string.audio_title),
                    colorFilter = ColorFilter.tint(colorResource(R.color.home_note_list_icon_hint_color)),
                    modifier = Modifier.size(100.dp)
                )
            }
        }

        ThumbnailType.TEXT_ICON -> {
            OnlyTextIcon()
        }

        ThumbnailType.MIXED_CONTENT -> {
            // 混合模式：底部富文本，上层叠加手绘
            // 使用优化后的实现，避免复杂的缩放计算
//            ModifierBasedMixedContentDisplay(note)
            val  containerSize= rememberScaledCardSize()
            // 根据夜间模式选择合适的缩略图
            val handwritingThumbnail = if (isSystemInDarkTheme()) {
                note.handwritingThumbnailDark ?: note.handwritingThumbnail
            } else {
                note.handwritingThumbnail
            }
            val imageCacheKey = "$handwritingThumbnail${note.modifyTime}"
            HomeThumbnailPreview(
                targetWidth = containerSize.width.dp,
                targetHeight = containerSize.height.dp,
            ) {
                HomeRichTextPreview(
                    note = note,
                    modifier = Modifier
                        .fillMaxSize(),
                    isNormalMode = false
                )
                HomeAsyncImage(
                    modifier = Modifier.fillMaxSize(),
                    imageUri = handwritingThumbnail,
                    imageCacheKey = imageCacheKey
                )
            }
        }
    }
}

/**
 *  显示文本图标
 */
@Composable
private fun OnlyTextIcon() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_note_only_text),
            contentDescription = stringResource(R.string.text_speaker),
            colorFilter = ColorFilter.tint(colorResource(R.color.home_note_list_icon_hint_color)),
            modifier = Modifier.size(100.dp)
        )
    }
}

@Composable
private fun AsyncImageWithFallback(note: HomeNoteItemModel) {

    // 优先级：手绘缩略图 > 首图 > image字段
    val handwritingThumbnail = note.handwritingThumbnail
    val firstPicture = note.firstPicture
    val image = note.image
    Logger.d("HomeNoteItemCard", "Selected handwritingThumbnail: $handwritingThumbnail $firstPicture $image")
    if (!handwritingThumbnail.isNullOrEmpty()) {
        val imageCacheKey = "$handwritingThumbnail${note.modifyTime}"
        HomeAsyncImage(
            modifier = Modifier.fillMaxSize(),
            imageUri = handwritingThumbnail,
            imageCacheKey = imageCacheKey
        )
    }else{
        val imageCacheKey = "$firstPicture${note.modifyTime}"
        HomeAsyncImage(
            modifier = Modifier.fillMaxSize(),
            imageUri = firstPicture?.toUri(),
            imageCacheKey = imageCacheKey
        )
    }
}

@Composable
fun HomeAsyncImage(
    modifier: Modifier = Modifier,
    imageUri: Any?,
    imageCacheKey: String
) {

    if (imageUri != null) {
        val context = LocalContext.current
        // 使用全局的ImageLoader，避免在每个列表项中重复创建
        val imageLoader = LocalContext.current.imageLoader.newBuilder().components {
            GifDecoder.Factory()
        }.build()
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(imageUri)
                .crossfade(true)
                .memoryCacheKey(imageCacheKey)
                .diskCacheKey(imageCacheKey)
                .build(),
            imageLoader = imageLoader,
            contentDescription = stringResource(R.string.image_title),
            contentScale = ContentScale.FillBounds, // 填充父容器
            modifier = modifier
        )
    }
}

@Composable
private fun MixedContentDisplay(note: HomeNoteItemModel) {
    val noteLayoutConfig = rememberNoteLayoutConfig()
    val cardSize = noteLayoutConfig.cardSize
    val cardWidth = cardSize.width.dp
    val cardHeight = cardSize.height.dp
    val screenSize = getScreenSize()
    val widthDp = screenSize.width().px2dp.dp
    val heightDp = screenSize.height().px2dp.dp
    var scaleXValue = cardWidth / widthDp
    var scaleYValue = cardHeight / heightDp
    if (isTabletLandscape) {
        scaleXValue = cardWidth / heightDp
        scaleYValue = cardHeight / widthDp
    }
    Logger.d("HomeNoteItemCard", "scaleXValue: $scaleXValue, scaleYValue: $scaleYValue")
    Logger.d("HomeNoteItemCard", "widthDp: $widthDp, heightDp: $heightDp")
    Logger.d("HomeNoteItemCard", "cardWidth: $cardWidth, cardHeight: $cardHeight")
    val textTransY = if (isTablet) {
        if (isTabletLandscape) -24.dp.toPx else -0.dp.toPx
    } else 10.dp.toPx
    val imageOffset = if (isTablet) {
        if (isTabletLandscape) IntOffset(
            y = (-28).dp.toPx.toInt(),
            x = (-5).dp.toPx.toInt()
        ) else IntOffset(y = (-42).dp.toPx.toInt(), x = (-5).dp.toPx.toInt())
    } else IntOffset(y = (-25).dp.toPx.toInt(), x = (-5).dp.toPx.toInt())
    Box(modifier = Modifier.fillMaxSize()) {
        if (isTabletLandscape) {
            HomeRichTextPreview(
                note = note,
                modifier = Modifier
                    .requiredSize(heightDp)
                    .graphicsLayer {
                        translationY = textTransY
                        transformOrigin = TransformOrigin(0.5f, 0.5f)
                        scaleX = scaleXValue
                        scaleY = scaleYValue
                    }
                    .align(Alignment.TopCenter),
                isNormalMode = false
                // 启用缩略图模式
            )
        } else {
            HomeRichTextPreview(
                note = note,
                modifier = Modifier
                    .requiredSize(widthDp, heightDp)
                    .graphicsLayer {
                        translationY = textTransY
                        transformOrigin = TransformOrigin(0.5f, 0.5f)
                        scaleX = scaleXValue
                        scaleY = scaleYValue
                    }
                    .align(Alignment.TopCenter),
                isNormalMode = false
                // 启用缩略图模式
            )
        }
        OptimizedMixedContentHandwritingDisplay(
            note = note,
            modifier = Modifier
                .requiredSize(widthDp)
                .graphicsLayer {
                    translationY = textTransY
                    transformOrigin = TransformOrigin(0.5f, 0.5f)
                    scaleX = scaleXValue
                    scaleY = scaleYValue
                }
        )
    }
}
