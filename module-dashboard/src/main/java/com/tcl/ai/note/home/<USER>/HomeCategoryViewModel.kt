package com.tcl.ai.note.home.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.data.GetUserSettingUseCase
import com.tcl.ai.note.home.model.HomeCategoryItemModel
import com.tcl.ai.note.data.UserSavedCategoryInfo
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogCallBackEvent
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogEvent
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogEventManager
import com.tcl.ai.note.handwritingtext.ui.categorydialog.CategoryDialogType
import com.tcl.ai.note.home.data.HomeDataManager
import com.tcl.ai.note.handwritingtext.database.HomeRepository
import com.tcl.ai.note.home.utils.TYPE_UN_CATEGORISED_ID
import com.tcl.ai.note.home.utils.mapToCategoriesList
import com.tcl.ai.note.home.utils.translateToCategory
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.state.HomeCategoryUiState
import com.tcl.ai.note.home.vm.action.HomeCategoryEvent
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.toComposeColor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.util.Locale


/**
 *分类 ViewModel
 * 负责获取分类数据和处理用户交互，
 * 创建分类和分类相关的功能
 */
class HomeCategoryViewModel() : ViewModel() {
    private val TAG = "HomeCategoryViewModel"
    // 为null 则是不选择任何分类 为""则是全部分类
    private val _selectedCategoryIdFlow = MutableStateFlow<String?>("") 
    private val selectedCategoryIdFlow: StateFlow<String?> = _selectedCategoryIdFlow.asStateFlow()
    
    private val _homeCategoryUiState = MutableStateFlow(
        HomeCategoryUiState()
    )
    val homeCategoryUiState: StateFlow<HomeCategoryUiState> = _homeCategoryUiState.asStateFlow()
    
    //一次性事件
    private val _effect = MutableSharedFlow<HomeCategoryEvent>(
        replay = 0,              // 不重放历史事件
        extraBufferCapacity = 1  // 缓冲1个事件，防止事件丢失
    )
    val effect: SharedFlow<HomeCategoryEvent> = _effect.asSharedFlow()

    private var isCategoryListLoaded = false
    private var cachedCategoryItems: List<HomeCategoryItemModel>? = null
    private var lastSelectedCategoryID: UserSavedCategoryInfo? = null

    init {
        observeCategoryList()
        observeCategoryDialogCallBackEvents()
        
        // 监听选中分类ID的变化，同步更新UI状态
        viewModelScope.launch {
            selectedCategoryIdFlow.collect { categoryId ->
                categoryId?.let { id ->
                    _homeCategoryUiState.update { state ->
                        state.copy(selectedCategoryId = id)
                    }
                    // 如果分类列表已加载，则更新分类列表
                    if (isCategoryListLoaded && cachedCategoryItems != null) {
                        updateCategoryList(cachedCategoryItems!!)
                    }
                }
            }
        }
    }

    // 更新选中的分类ID，统一入口
    private fun updateSelectedCategoryId(categoryId: String?) {
        Logger.d(TAG, "updateSelectedCategoryId: $categoryId")
        _selectedCategoryIdFlow.value = categoryId
    }

    private fun observeCategoryDialogCallBackEvents() {
        viewModelScope.launch {
            CategoryDialogEventManager.categoryDialogSuccessEvents.collect { event ->
                if (event is CategoryDialogCallBackEvent.OnCategoryCreatedCallBack || event is CategoryDialogCallBackEvent.OnCategoryNoteMoved) {
                    Logger.d(TAG, "Received new category callback: categoryId=${event.categoryId}")
                } else if(event is CategoryDialogCallBackEvent.OnCategoryDialogDismiss){
                    Logger.d(TAG, "Received category pop window dismiss callback: categoryId=${event.categoryId}")
                }
            }
        }
    }

    private var loadCategoryListJob: Job? = null



    fun stopObservingCategoryList() {
        loadCategoryListJob?.cancel()
        loadCategoryListJob = null
    }
    @OptIn(ExperimentalCoroutinesApi::class)
    private fun observeCategoryList() {
        // 先取消之前的监听
        loadCategoryListJob?.cancel()
        loadCategoryListJob=viewModelScope.launch {
            // 直接监听分类列表变化，已经内置了笔记数量监听
            HomeRepository.getAllCategoriesList().mapLatest { dbCategories ->
                // 获取总笔记数量（用于"全部"分类）
                mapToCategoriesList(dbCategories)
            }
                .catch { emit(emptyList()) }
                .flowOn(Dispatchers.IO)
                .collect { categories ->
                    cachedCategoryItems = categories
                    HomeDataManager.globalCachedCategoryItems = cachedCategoryItems
                    isCategoryListLoaded = true
                    updateSelectedCategoryId(lastSelectedCategoryID?.id ?: "")
                    updateCategoryListIfReady()
                }
        }
    }


    /**
     * 只有在两个数据源都准备好后才更新分类列表，避免重复刷新
     */
    private fun updateCategoryListIfReady() {
        if (isCategoryListLoaded && cachedCategoryItems != null) {
            updateCategoryList(cachedCategoryItems!!)
        }
    }

    private var currentLocale: Locale? = null

    private fun updateLocale(newLocale: Locale?) {
        if (newLocale != currentLocale) {
            currentLocale = newLocale
            observeCategoryList()
        }
    }

    /**
     * 搜索时去掉选中状态，否则还原选中状态
     */
    fun onChangeCategoryState(isSearchMode: Boolean) {
        if (isSearchMode) {
            //去掉选中状态
            updateSelectedCategoryId(null)
        } else {
            //还原选中状态
            updateSelectedCategoryId(lastSelectedCategoryID?.id ?: "")
        }
        cachedCategoryItems?.let {
            updateCategoryList(it,false)
        }
    }

    /**
     * 更新分类列表，并标记选中的分类
     */
    private fun updateCategoryList(categoryItems: List<HomeCategoryItemModel>, isSendCategoryChangeEvent: Boolean = true) {
        val selectedCategoryID = selectedCategoryIdFlow.value
        Logger.d(TAG,"updateCategoryList  $selectedCategoryID")
        val updatedCategories = categoryItems.map { category ->
            category.copy(
                isSelected = category.id == selectedCategoryID,
                isLongClickSelected = false
            )
        }
        _homeCategoryUiState.update { uiState ->
            uiState.copy(categories = updatedCategories)
        }
        saveCurrentCategoryInfo(isSendCategoryChangeEvent)
    }

    /**
     * 保存当前选中的分类信息
     */
    private fun saveCurrentCategoryInfo(isSendCategoryChangeEvent: Boolean = true) {
        viewModelScope.launchIO {
            _homeCategoryUiState.value.categories.find { it.isSelected }?.let {category->
                lastSelectedCategoryID = UserSavedCategoryInfo(
                    id = category.id,
                    name = category.name ?: "",
                    colorIndex = "",
                    icon = ""
                )
                if(isSendCategoryChangeEvent) {
                    _effect.emit(HomeCategoryEvent.OnCategoryChange(changeCategoryItem = category))
                }
            }
            //很关键 不然当前分类如果没有便签的话，进富文本页面，不会自动移动到该分类下。
            GetUserSettingUseCase.saveUserCategoryInfo(lastSelectedCategoryID!!)
        }
    }

    /**
     * 处理分类相关操作
     */
    fun onCategoryAction(action: HomeCategoryAction) {
        when (action) {
            is HomeCategoryAction.OnCategorySelected -> {
                updateSelectedCategoryId(action.category.id)
                updateCategoryList(cachedCategoryItems!!,false)
            }

            is HomeCategoryAction.OnCreateNewCategoryClick -> {
                viewModelScope.launch {
                    CategoryDialogEventManager.sendCategoryDialogEvent(
                        CategoryDialogEvent(
                            type = CategoryDialogType.NEW_CATEGORY
                        )
                    )
                }

            }

            is HomeCategoryAction.OnRenameCategoryClick -> {
                viewModelScope.launch {
                    action.category.apply {
                        CategoryDialogEventManager.sendRenameCategoryEvent(
                            id = id,
                            name = name,
                            noteCounts = action.category.noteCounts,
                            icon = action.category.categoryIcon!!.icon,
                            colorIndex = colorIndex,
                            color = categoryIcon?.color?.toComposeColor()
                        )
                    }
                }
            }
            //长按选中 效果
            is HomeCategoryAction.OnLongClickSelected -> {
                updateLongClickState(action.selectedCategory.id, action.isLongClickSelected)
            }

            is HomeCategoryAction.OnDeleteCategoryClick -> {
                deleteCategory(action.isDeleteNotesSelected,action.category)
            }
            is HomeCategoryAction.OnShowDeleteCategoryNotesDialog -> {
                _homeCategoryUiState.update {
                    it.copy(
                        isShowDeleteCategoryDialog = action.isShowDialog
                    )
                }
            }
            //切换语言，主动刷新分类列表
            is HomeCategoryAction.OnLocaleChange ->{
                updateLocale(action.newLocale)
            }

            else -> {}
        }
    }

    private fun updateLongClickState(selectCategoryId: String, isLongClickSelected: Boolean) {
        Logger.d(TAG, "updateLongClickState: $selectCategoryId $isLongClickSelected")
        _homeCategoryUiState.update {
            it.copy(
                categories = it.categories.map { category ->
                    if (category.id == selectCategoryId) {
                        category.copy(isLongClickSelected = isLongClickSelected)
                    } else {
                        category
                    }
                }
            )
        }
    }


    /**
     * 删除分类 带位置上移
     */
    private fun deleteCategory(
        isDeleteSelectedCategoryNotes: Boolean,
        toDeleteCategory: HomeCategoryItemModel,
    ) {
        val noteCategory = translateToCategory(toDeleteCategory)
        viewModelScope.launch {
            deleteCategoryFromDB(noteCategory, isDeleteSelectedCategoryNotes, toDeleteCategory)
            val selectIndex = _homeCategoryUiState.value.categories.indexOfFirst { it.id == toDeleteCategory.id }
            //删除成功后，选中Item的位置往上移一位,如果待删除的和选中的不是同一个，则不需要位移
            val categoryUiState = _homeCategoryUiState.value
            Logger.d(
                TAG,
                "OnDeleteCategoryClick: ${toDeleteCategory.id} ${categoryUiState.selectedCategoryId} $selectIndex"
            )

            // 只有当删除的是当前选中的分类时，才需要更新选中状态
            if (categoryUiState.selectedCategoryId != toDeleteCategory.id) {
                return@launch
            }

            // 使用 coerceIn 确保索引在有效范围内
            val categories = categoryUiState.categories
            val safeIndex = (selectIndex - 1).coerceIn(0, categories.size - 1)
            val beforeHomeCategoryItem = categories[safeIndex]

            // 使用统一的方法更新选中的分类ID
            updateSelectedCategoryId(beforeHomeCategoryItem.id)

            Logger.d(
                TAG,
                "OnDeleteCategoryClick: ${toDeleteCategory.id} ${categoryUiState.selectedCategoryId} ${beforeHomeCategoryItem.id}"
            )

            // 发送分类选中事件
            _effect.emit(HomeCategoryEvent.OnCategoryChange(beforeHomeCategoryItem))
        }
    }

    private suspend fun deleteCategoryFromDB(
        noteCategory: NoteCategory,
        isDeleteSelectedCategoryNotes: Boolean,
        toDeleteCategory: HomeCategoryItemModel
    ) {
        val res = HomeRepository.deleteCategory(noteCategory)
        if (res > 0) {
            if (isDeleteSelectedCategoryNotes) {
                // 删除分类下的note数据
                HomeRepository.deleteNotesByCategoryId(noteCategory.categoryId)
            } else {
                // 将该分类下的数据迁移到未分类下面
                HomeRepository.updateCategoryId(
                    toDeleteCategory.id.toLong(),
                    TYPE_UN_CATEGORISED_ID
                )
            }
        }
    }


}
