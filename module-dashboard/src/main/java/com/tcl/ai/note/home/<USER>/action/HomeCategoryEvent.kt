package com.tcl.ai.note.home.vm.action

import com.tcl.ai.note.exception.AppException
import com.tcl.ai.note.home.model.HomeCategoryItemModel

/**
 * 一次性事件
 * ViewModel 发出的反馈或一次性副作用，用 Event 或 Effect 命名。
 */
sealed class HomeCategoryEvent {
    data class ShowToastRes(val resourceId: Int,val currentTime:Long=System.currentTimeMillis()) : HomeCategoryEvent()
    data class Exception(val exception: AppException) : HomeCategoryEvent()
    data class OnCategoryChange(val changeCategoryItem: HomeCategoryItemModel, val currentTime:Long=System.currentTimeMillis()) : HomeCategoryEvent()
}