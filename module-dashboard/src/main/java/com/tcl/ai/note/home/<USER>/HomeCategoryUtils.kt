package com.tcl.ai.note.home.utils

import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.home.model.CategoryIcon
import com.tcl.ai.note.home.model.HomeCategoryItemModel
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getCategoryIconColor
import com.tcl.ai.note.utils.getCategoryIconResId


/**
 * 未分类的分类ID
 */
const val TYPE_UN_CATEGORISED_ID = 1L

/**
 * 将数据库实体转换为UI数据模型
 */
fun mapToCategoriesList(savedNoteValue: Pair<List<NoteCategory>, Int>): List<HomeCategoryItemModel> {
    val totalNoteCount = savedNoteValue.second
    val savedCategories=savedNoteValue.first
    Logger.d(
        "HomeCategoryViewModel",
        "loadCategoryList: totalNoteCount: $totalNoteCount " + Thread.currentThread().name
    )
    // 将分类数据转换为 CategoryItem
    val categoryItems = savedCategories.map { category ->
        val categoryIcon = getCategoryIcon(category.colorIndex)
        HomeCategoryItemModel(
            name = category.name,
            categoryIcon = categoryIcon,
            id = category.categoryId.toString(),
            noteCounts = category.noteCounts,
            colorIndex = category.colorIndex,
        )
    }.toMutableList().apply {
        // 在开头添加"全部"分类，未分类改成在全部后面
        // 1. 查找id为"1"的项既：“未分类”（注意id是字符串类型，需与实际类型匹配）
        val targetItem = find { it.id == "1" }
        // 2. 确保"全部"分类在首位
        val elementAllItem = allCategoryItem(totalNoteCount)
        // 3. 如果列表中已有"全部"分类，先移除避免重复
        removeIf { it.id == elementAllItem.id }
        // 4. 添加"全部"分类到首位
        add(0, elementAllItem)
        // 5. 如果找到目标项，先移除再插入到索引1的位置
        if (targetItem != null) {
            remove(targetItem)
            // 6. 将目标项（id=1）插入到第二个位置（索引1）
            add(1, targetItem)
        }
    }
    return categoryItems.toList()
}
/**
 * 获取"全部"分类项
 */
fun allCategoryItem(totalNoteCount: Int): HomeCategoryItemModel {
    // 在开头添加"全部"分类
    val elementAllItem = HomeCategoryItemModel(
        categoryIcon = CategoryIcon(R.drawable.ic_all_notes, getCategoryIconColor(-1)),
        noteCounts = totalNoteCount,
        id = "", // 全部分类的ID为空字符串,
    )
    return elementAllItem
}
/**
 * 将 HomeCategoryItem 转换为 NoteCategory
 */
fun translateToCategory(
    categoryItem: HomeCategoryItemModel,
): NoteCategory {
    val noteCategory = NoteCategory(
        categoryId = categoryItem.id.toLong(),
        colorIndex = categoryItem.colorIndex,
        name = categoryItem.name ?: ""
    )
    return noteCategory
}

/**
 * 获取分类图标
 */
 fun getCategoryIcon(colorIndex: Int): CategoryIcon {
    return CategoryIcon(getCategoryIconResId(colorIndex), getCategoryIconColor(colorIndex))
}