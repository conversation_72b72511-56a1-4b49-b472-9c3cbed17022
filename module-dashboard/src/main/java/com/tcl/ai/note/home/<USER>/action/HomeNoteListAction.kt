package com.tcl.ai.note.home.vm.action

import com.tcl.ai.note.home.components.NavigationTab
import com.tcl.ai.note.home.vm.state.HomeTitleMode

/**
 * 用户操作/意图：用 Action 或 Intent
 * 右侧笔记相关操作
 */
sealed class HomeNoteListAction {
    data class OnItemCheckedChange(val noteId: String, val checked: Boolean) : HomeNoteListAction()
    //长按选中
    data class OnItemLongClick(val noteId: String) : HomeNoteListAction()
    //新建笔记和跳转笔记
    data class OnAddHomeNoteClick(val noteId: String, val isPen: Boolean = false) : HomeNoteListAction()
    data object OnChangeViewType : HomeNoteListAction()
    //标题模式
    data class OnChangeTitleMode(val titleMode: HomeTitleMode) : HomeNoteListAction()
    //排序
    data class OnChangeSortType(val isCreateTimeSort: Boolean?=null) : HomeNoteListAction()
    //点击了排序
    data class OnClickSort(val clickSorted:Boolean) : HomeNoteListAction()
    //全选
    data class OnSelectAll(val isSelectAll: Boolean) : HomeNoteListAction()
    //搜索
    data class OnSearchTextChange(val searchText: String) : HomeNoteListAction()
    //删除
    data object OnDelete : HomeNoteListAction()
    // 移动到分类
    data object OnMoveToCategory : HomeNoteListAction()
    // show dialog 删除笔记弹窗
    data class OnShowDeleteDialog(val isShowDialog: Boolean=false) : HomeNoteListAction()

    data class OnNavigateTo(val tab: NavigationTab) : HomeNoteListAction()
    // 打开抽屉
    data object OnOpenDrawer : HomeNoteListAction()
    // 时间格式变化，需要重新加载数据以更新时间显示
    data class OnTimeFormatChanged(val is24Hour: Boolean) : HomeNoteListAction()
}

