package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.home.components.HighlightText
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.utils.Logger

/**
 * 富文本预览组件
 * 用于在首页卡片中显示富文本内容，支持异步加载富文本样式
 */
@Composable
fun HomeRichTextPreview(
    modifier: Modifier = Modifier,
    note: HomeNoteItemModel,
    isNormalMode: Boolean = true
) {
    val darkTheme = isSystemInDarkTheme()
    val richTextStyleEntity = note.richTextStyleEntity
    val noteLayoutConfig = rememberNoteLayoutConfig()

    when {
        richTextStyleEntity != null && !note.content.isNullOrBlank()&& !note.summary.isNullOrBlank() -> {
            // 有富文本样式，使用富文本显示
            RichTextDisplay(
                content = if (isNormalMode) note.summary else note.content,
                richTextStyleEntity = note.richTextStyleEntity,
                modifier = modifier,
                darkTheme = darkTheme,
                isNormalMode = isNormalMode,
                maxLines = if(isNormalMode) noteLayoutConfig.contentMaxLines else Int.MAX_VALUE,
            )
        }
        
        else -> {
            // 没有富文本样式，使用普通文本显示
            FallbackTextDisplay(note, modifier)
        }
    }
}
/**
 * Compose 版本的富文本显示组件
 * 用于在 Compose UI 中显示富文本内容
 */
@Composable
fun RichTextDisplay(
    content: String,
    richTextStyleEntity: RichTextStyleEntity?,
    modifier: Modifier = Modifier,
    darkTheme: Boolean = isSystemInDarkTheme(),
    maxLines: Int = 15,
    isNormalMode: Boolean = true
) {
    val context = LocalContext.current

    // 记住 RichTextDisplayView 实例，只在关键参数变化时重新创建
    val richTextView = remember(maxLines, isNormalMode) {
        RichTextDisplayView(context, maxLines, isNormalMode)
    }

    // 当内容或样式变化时更新显示
    LaunchedEffect(content, richTextStyleEntity) {
        try {
            richTextView.setRichTextContent(content, richTextStyleEntity)
        } catch (e: Exception) {
            Logger.e("RichTextDisplay", "Error updating content: ${e.message}")
        }
    }

    // 当夜间模式变化时更新颜色
    LaunchedEffect(darkTheme) {
        richTextView.updateDarkMode()
    }

    AndroidView(
        factory = { richTextView },
        modifier = modifier.fillMaxSize(),
        update = { view ->
        }
    )
}

/**
 * 备用文本显示组件
 * 当没有富文本样式或加载失败时使用
 */
@Composable
private fun FallbackTextDisplay(
    note: HomeNoteItemModel,
    modifier: Modifier = Modifier
) {
    val displayText = when {
        !note.summary.isNullOrBlank() -> note.summary
        !note.content.isNullOrBlank() -> note.content
        else -> ""
    }
    
    HighlightText(
        text = displayText,
        modifier = modifier,
        normalStyle = TextStyle(
            fontSize = 11.sp,  // 与 RichTextDisplayView 保持一致
            lineHeight = 16.sp, // 与 RichTextDisplayView 保持一致
            color = colorResource(R.color.home_title_color)
        ),
        maxLines = 6,  // 与 RichTextDisplayView 保持一致的最大行数
        highlights = note.highlightInfo?.contentHighlights ?: emptyList()
    )
}
