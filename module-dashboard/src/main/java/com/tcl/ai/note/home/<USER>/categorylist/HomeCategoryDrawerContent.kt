package com.tcl.ai.note.home.components.categorylist

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.dashboard.ui.BottomDeleteCategoryDialog
import com.tcl.ai.note.home.components.AdapterStatusBar
import com.tcl.ai.note.home.components.HomeCategoryAddCategoryTextStyle
import com.tcl.ai.note.home.components.HomeTitleTextStyle
import com.tcl.ai.note.home.components.rightBorder
import com.tcl.ai.note.home.data.demoCategories
import com.tcl.ai.note.home.model.HomeCategoryItemModel
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.state.HomeCategoryUiState
import com.tcl.ai.note.setting.version.UpdateMonitorImpl
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.utils.isButtonNavigation
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.clickableHover
import com.tcl.ai.note.widget.components.BottomFadeColumn
import java.util.Locale

/**
 * 左侧分类面板组件
 */
@Composable
fun HomeCategoryDrawerContent(
    modifier: Modifier = Modifier,
    categoryUiState: HomeCategoryUiState,
    onCategoryAction: (HomeCategoryAction) -> Unit,
    updateIconContent: @Composable () -> Unit = {}
) {

    val listState = rememberLazyListState()

    //是否滑动最后
    val isScrolledToBottom by remember {
        derivedStateOf {
            listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index == listState.layoutInfo.totalItemsCount - 1
        }
    }
    val categoryList = categoryUiState.categories

    // 已经展开的菜单项ID
    var expandedCategoryId by rememberSaveable { mutableStateOf<String?>(null) }

    val configuration = LocalConfiguration.current
    val currentLocale = configuration.locales.get(0) ?: Locale.getDefault()

    // 语言类型发生变化
    LaunchedEffect(currentLocale) {
        Logger.d("HomeCategoryDrawerContent", "Locale changed to: $currentLocale")
        onCategoryAction(HomeCategoryAction.OnLocaleChange(currentLocale))
    }

    // 监听长按状态变化，当没有长按选中时关闭所有菜单
    LaunchedEffect(categoryUiState.categories) {
        val hasLongClickSelected = categoryUiState.categories.any { it.isLongClickSelected }
        if (!hasLongClickSelected) {
            expandedCategoryId = null
        }
    }

    val hasNavigationBar = isButtonNavigation() //为true 代表是按键导航，false为手势导航（横线）
    val navigationBarHeight = getNavigationBarHeight()

    val paddingBottom = if (hasNavigationBar) 32.dp else navigationBarHeight
    //导航分为两种，手势导航和按键导航，手势导航以现有效果为准，需要单独适配按键导航
    Logger.i(
        "HomeCategoryDrawerContent",
        "paddingBottom:$paddingBottom, hasNavigationBar:$hasNavigationBar, navigationBarHeight:$navigationBarHeight"
    )

    // 监听列表滑动：滑动时关闭弹窗
    LaunchedEffect(listState.isScrollInProgress) {
        if (listState.isScrollInProgress) {
            expandedCategoryId = null
        }
    }

    BottomFadeColumn(
        modifier = modifier
            .fillMaxSize()
            .background(color = colorResource(R.color.home_category_bg_color))
            .rightBorder(
                width = 1.dp,
                color = colorResource(R.color.right_border_color)
            )
            .padding(start = 12.dp, end = 12.dp, bottom = navigationBarHeight),
        isDrawGradient = !isScrolledToBottom,
        isButtonNavigation = hasNavigationBar
    ) {
        CategoryTitle(updateIconContent)
        LazyColumn(
            modifier = Modifier
                .fillMaxSize(),
            state = listState
        ) {

            // 主要分类列表
            itemsIndexed(
                items = categoryList,
                key = { _, item -> item.id }
            ) { index, category ->
                // 获取真实的数据索引，而不是显示索引
                CategoryItemRow(
                    modifier = Modifier.animateItem(),
                    category = category,
                    onAction = onCategoryAction,
                    onClick = { onCategoryAction(HomeCategoryAction.OnCategorySelected(category)) },
                    isDropdownMenuExpanded = expandedCategoryId == category.id,
                    onMenuExpandedChange = { isExpanded ->
                        expandedCategoryId = if (isExpanded) {
                            category.id
                        } else null
                    }
                )
            }

            item {
                // 新建文件夹按钮
                CategoryBottom(onCategoryAction)
            }
        }
    }
    DeleteCategoryDialog(categoryUiState, categoryList, onCategoryAction)
}

// 删除分类弹窗
@Composable
private fun DeleteCategoryDialog(
    categoryUiState: HomeCategoryUiState,
    categoryList: List<HomeCategoryItemModel>,
    onCategoryAction: (HomeCategoryAction) -> Unit
) {
    if (categoryUiState.isShowDeleteCategoryDialog) {
        var isDeleteNotesSelected by rememberSaveable { mutableStateOf(false) }
        val selectCategory = categoryList.find { it.isLongClickSelected }
        val index = categoryList.indexOfFirst { it.isLongClickSelected }
        Logger.d("HomeCategoryDrawerContent", "selectCategory: $selectCategory index: $index ")
        val category = selectCategory ?: return
        BottomDeleteCategoryDialog(
            onDelete = {
                onCategoryAction(
                    HomeCategoryAction.OnDeleteCategoryClick(
                        isDeleteNotesSelected,
                        category
                    )
                )
                onCategoryAction(HomeCategoryAction.OnLongClickSelected(category, false))
                onCategoryAction(HomeCategoryAction.OnShowDeleteCategoryNotesDialog(false))
            },
            onCancel = {
                onCategoryAction(HomeCategoryAction.OnLongClickSelected(category, false))
                onCategoryAction(HomeCategoryAction.OnShowDeleteCategoryNotesDialog(false))
            },
            isDeleteNotesSelected = {
                isDeleteNotesSelected = it
            },
            isShowDeleteNotes = category.noteCounts > 0
        )
    }
}

@Composable
private fun CategoryBottom(onAction: (HomeCategoryAction) -> Unit) {
    Spacer(Modifier.height(12.dp))
    Row(
        modifier = Modifier
            .padding(horizontal = 12.dp)
            .fillMaxWidth()
            .height(44.dp)
            .clip(RoundedCornerShape(30.dp))
            .clickableHover(role = Role.Button) {
                onAction(HomeCategoryAction.OnCreateNewCategoryClick(true))
            }
            .background(
                colorResource(id = R.color.home_category_add_btn_bg)
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.dialog_category_name_title),
            color = colorResource(R.color.search_highlight),
            style = HomeCategoryAddCategoryTextStyle,
            modifier = Modifier.padding(horizontal = 16.dp),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

@Composable
private fun CategoryTitle(updateIconContent: @Composable () -> Unit) {
    AdapterStatusBar()
    Row(
        modifier = Modifier
            .height(56.dp)
            .fillMaxWidth()
            .padding(start = 12.dp, end = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(
            text = stringResource(R.string.app_name),
            style = HomeTitleTextStyle,
            color = colorResource(R.color.home_title_color)
        )
        updateIconContent()
    }

    // 只有在平板上显示Spacer
    if (isTablet) {
        Spacer(Modifier.height(6.dp))
    }
}


@Preview
@Composable
private fun HomeCategoryDrawerContentPreview() {
    HomeCategoryDrawerContent(
        categoryUiState = HomeCategoryUiState(categories = demoCategories),
        onCategoryAction = {},
        updateIconContent = {
            UpdateIconWithDot({}, UpdateMonitorImpl.UpdateState.Idle, false)
        }
    )
}