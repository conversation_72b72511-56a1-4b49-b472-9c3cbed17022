package com.tcl.ai.note.home.components.notelist.title

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.tryToRequestFocus
import com.tcl.ai.note.widget.HoverProofIconButton

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun HomeNoteListTopTitleSearch(
    modifier: Modifier = Modifier,
    onTextChange: (String,Boolean) -> Unit,
    onBack: () -> Unit
) {
    var text by rememberSaveable { mutableStateOf("") }
    val context = LocalContext.current
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val isKeyboardVisible = WindowInsets.isImeVisible

    LaunchedEffect(isKeyboardVisible) {
        if (!isKeyboardVisible) {
            focusManager.clearFocus()
        }
    }

    // 例如设置焦点到输入框
    LaunchedEffect(Unit) {
        // 在组件首次加载时，设置焦点到输入框
        if (text.isEmpty()) {
            focusRequester.tryToRequestFocus()
            onTextChange(text,true)
        }
    }
    val backString = stringResource(R.string.edit_top_menu_back_icon)
    Row(
        verticalAlignment = Alignment.CenterVertically,
    ) {
        HoverProofIconButton(
            modifier = Modifier
                .size(32.dp)
                .clearAndSetSemantics {
                    contentDescription = backString
                    this.role = Role.Button
                },
            onClick = {
                onBack()
            }
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_note_back),
                contentDescription = backString,
            )
        }

        Spacer(modifier = Modifier.width(if (isTablet) 16.dp else 8.dp))
        // 输入框区域
        Box(
            modifier = Modifier
                .invisibleSemantics()
                .height(44.dp)
                .clip(RoundedCornerShape(24.dp))
                .background(colorResource(R.color.home_search_bg))
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.CenterStart
        ) {
                BasicTextField(
                    value = text,
                    modifier = Modifier
                        .fillMaxWidth()
                        .semantics {
                            contentDescription = context.getString(R.string.search_notes)
                        }
                        .focusRequester(focusRequester),
                    singleLine = true,
                    cursorBrush = SolidColor(colorResource(R.color.text_field_border)),
                    textStyle = TextStyle(
                        color = colorResource(R.color.dialog_title),
                        fontSize = 14.sp,
                        lineHeight = 18.sp, // 设置固定行高，确保中英文高度一致
                    ),
                    onValueChange = {
                        text = it
                        onTextChange(it,true)  // 更新文本
                    },
                    decorationBox = { innerTextField ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // 只有平板才有搜索图标
                            if(isTablet){
                                Image(
                                    painter = painterResource(R.drawable.ic_home_note_search),
                                    contentDescription = stringResource(R.string.search_notes),
                                    modifier = Modifier.size(24.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                            }

                            Box(
                                modifier = Modifier.weight(1f),
                                contentAlignment = Alignment.CenterStart
                            ) {
                                // 占位符
                                if (text.isEmpty()) {
                                    Text(
                                        text = stringResource(R.string.search_notes),
                                        style = TextStyle(
                                            fontSize = 14.sp,
                                            lineHeight = 18.sp,
                                            color = colorResource(R.color.text_category_placeholder)
                                        ),
                                        modifier = Modifier.invisibleSemantics()
                                    )
                                }
                                innerTextField()
                            }
                            Spacer(modifier = Modifier.width(4.dp))
                            // 清除按钮
                            if (text.isNotEmpty()) {
                                HoverProofIconButton(
                                    modifier = Modifier.size(20.dp),
                                    onClick = {
                                        text = ""
                                        onTextChange("",true)  // 清除文本
                                    }
                                ) {
                                    Image(
                                        painter = painterResource(R.drawable.ic_close),
                                        contentDescription = stringResource(R.string.delete),
                                    )
                                }
                            }
                        }
                    }
                )
        }

    }
}

@Preview(showBackground = true)
@Composable
private fun HomeNoteSearchPreview() {
    HomeNoteListTopTitleSearch(
        onTextChange = { _,_ -> },
        onBack = {}
    )
}